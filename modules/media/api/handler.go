package api

import (
	"fmt"
	"net/http"
	"strconv"

	"wnapi/internal/core"
	"wnapi/internal/pkg/response"
	"wnapi/modules/media/dto"
	"wnapi/modules/media/internal"

	"github.com/gin-gonic/gin"
)

// Handler là đối tượng ch<PERSON>h xử lý API cho module Media
type Handler struct {
	mediaService       internal.MediaService
	mediaFolderService internal.MediaFolderService
	routes             []string
}

// NewHandler tạo một handler mới
func NewHandler(mediaService internal.MediaService, mediaFolderService internal.MediaFolderService) *Handler {
	return &Handler{
		mediaService:       mediaService,
		mediaFolderService: mediaFolderService,
		routes:             make([]string, 0),
	}
}

// RegisterRoutes đăng ký tất cả routes cho module Media
func (h *Handler) RegisterRoutes(server *core.Server) error {
	// API Group
	apiGroup := server.Group("/api/v1/media")

	// L<PERSON>u lại danh sách các route để hiển thị
	basePath := "/api/v1/media"

	// Health check endpoint
	apiGroup.GET("/healthy", h.healthCheck)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/healthy", basePath))

	// Media routes
	apiGroup.POST("/upload", h.uploadMedia)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/upload", basePath))

	apiGroup.GET("/:id", h.getMedia)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/:id", basePath))

	apiGroup.GET("", h.listMedia)
	h.routes = append(h.routes, fmt.Sprintf("GET %s", basePath))

	apiGroup.PUT("/:id", h.updateMedia)
	h.routes = append(h.routes, fmt.Sprintf("PUT %s/:id", basePath))

	apiGroup.DELETE("/:id", h.deleteMedia)
	h.routes = append(h.routes, fmt.Sprintf("DELETE %s/:id", basePath))

	apiGroup.GET("/:id/download", h.downloadMedia)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/:id/download", basePath))

	// Folder routes
	folderGroup := apiGroup.Group("/folders")
	folderBasePath := basePath + "/folders"

	folderGroup.POST("", h.createFolder)
	h.routes = append(h.routes, fmt.Sprintf("POST %s", folderBasePath))

	folderGroup.GET("/:id", h.getFolder)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/:id", folderBasePath))

	folderGroup.GET("", h.listFolders)
	h.routes = append(h.routes, fmt.Sprintf("GET %s", folderBasePath))

	folderGroup.PUT("/:id", h.updateFolder)
	h.routes = append(h.routes, fmt.Sprintf("PUT %s/:id", folderBasePath))

	folderGroup.DELETE("/:id", h.deleteFolder)
	h.routes = append(h.routes, fmt.Sprintf("DELETE %s/:id", folderBasePath))

	return nil
}

// ListRoutes trả về danh sách tất cả các routes đã đăng ký
func (h *Handler) ListRoutes() []string {
	return h.routes
}

// PrintRoutes in ra console danh sách tất cả các routes đã đăng ký
func (h *Handler) PrintRoutes() {
	fmt.Println("=== MEDIA MODULE ROUTES ===")
	for _, route := range h.routes {
		fmt.Println(route)
	}
	fmt.Println("===========================")
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"module":  "media",
		"message": "Media module is running",
	})
}

// uploadMedia xử lý upload media file
func (h *Handler) uploadMedia(c *gin.Context) {
	// TODO: Get tenantID and userID from context/middleware
	tenantID := uint(1) // Temporary hardcoded value
	userID := uint(1)   // Temporary hardcoded value

	// Get file from form
	file, err := c.FormFile("file")
	if err != nil {
		response.Error(c, http.StatusBadRequest, "Không thể đọc file", "INVALID_FILE")
		return
	}

	// Parse request
	var req dto.UploadMediaRequest
	if err := c.ShouldBind(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "VALIDATION_ERROR")
		return
	}

	// Call service
	resp, err := h.mediaService.Upload(c.Request.Context(), tenantID, userID, file, req)
	if err != nil {
		errResp := internal.GetErrorResponse(err)
		response.Error(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode)
		return
	}

	response.Success(c, resp, nil)
}

// getMedia lấy thông tin media theo ID
func (h *Handler) getMedia(c *gin.Context) {
	// TODO: Get tenantID from context/middleware
	tenantID := uint(1) // Temporary hardcoded value

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID không hợp lệ", "INVALID_ID")
		return
	}

	resp, err := h.mediaService.GetByID(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		errResp := internal.GetErrorResponse(err)
		response.Error(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode)
		return
	}

	response.Success(c, resp, nil)
}

// listMedia lấy danh sách media
func (h *Handler) listMedia(c *gin.Context) {
	// TODO: Get tenantID from context/middleware
	tenantID := uint(1) // Temporary hardcoded value

	var req dto.ListMediaRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "Tham số không hợp lệ", "VALIDATION_ERROR")
		return
	}

	// Set default limit if not provided
	if req.Limit == 0 {
		req.Limit = 20
	}

	resp, err := h.mediaService.List(c.Request.Context(), tenantID, req)
	if err != nil {
		errResp := internal.GetErrorResponse(err)
		response.Error(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode)
		return
	}

	response.Success(c, resp, nil)
}

// updateMedia cập nhật thông tin media
func (h *Handler) updateMedia(c *gin.Context) {
	// TODO: Get tenantID from context/middleware
	tenantID := uint(1) // Temporary hardcoded value

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID không hợp lệ", "INVALID_ID")
		return
	}

	var req dto.UpdateMediaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "VALIDATION_ERROR")
		return
	}

	resp, err := h.mediaService.Update(c.Request.Context(), tenantID, uint(id), req)
	if err != nil {
		errResp := internal.GetErrorResponse(err)
		response.Error(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode)
		return
	}

	response.Success(c, resp, nil)
}

// deleteMedia xóa media
func (h *Handler) deleteMedia(c *gin.Context) {
	// TODO: Get tenantID from context/middleware
	tenantID := uint(1) // Temporary hardcoded value

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID không hợp lệ", "INVALID_ID")
		return
	}

	permanent := c.Query("permanent") == "true"

	err = h.mediaService.Delete(c.Request.Context(), tenantID, uint(id), permanent)
	if err != nil {
		errResp := internal.GetErrorResponse(err)
		response.Error(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode)
		return
	}

	response.Success(c, gin.H{"message": "Media đã được xóa thành công"}, nil)
}

// downloadMedia tải xuống file media
func (h *Handler) downloadMedia(c *gin.Context) {
	// TODO: Get tenantID from context/middleware
	tenantID := uint(1) // Temporary hardcoded value

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID không hợp lệ", "INVALID_ID")
		return
	}

	data, contentType, err := h.mediaService.GetFile(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		errResp := internal.GetErrorResponse(err)
		response.Error(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode)
		return
	}

	c.Header("Content-Type", contentType)
	c.Data(http.StatusOK, contentType, data)
}

// createFolder tạo folder mới
func (h *Handler) createFolder(c *gin.Context) {
	// TODO: Get tenantID and userID from context/middleware
	tenantID := uint(1) // Temporary hardcoded value
	userID := uint(1)   // Temporary hardcoded value

	var req dto.CreateFolderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "VALIDATION_ERROR")
		return
	}

	resp, err := h.mediaFolderService.Create(c.Request.Context(), tenantID, userID, req)
	if err != nil {
		errResp := internal.GetErrorResponse(err)
		response.Error(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode)
		return
	}

	response.Success(c, resp, nil)
}

// getFolder lấy thông tin folder theo ID
func (h *Handler) getFolder(c *gin.Context) {
	// TODO: Get tenantID from context/middleware
	tenantID := uint(1) // Temporary hardcoded value

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID không hợp lệ", "INVALID_ID")
		return
	}

	resp, err := h.mediaFolderService.GetByID(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		errResp := internal.GetErrorResponse(err)
		response.Error(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode)
		return
	}

	response.Success(c, resp, nil)
}

// listFolders lấy danh sách folder
func (h *Handler) listFolders(c *gin.Context) {
	// TODO: Get tenantID from context/middleware
	tenantID := uint(1) // Temporary hardcoded value

	var req dto.ListFolderRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "Tham số không hợp lệ", "VALIDATION_ERROR")
		return
	}

	// Set default limit if not provided
	if req.Limit == 0 {
		req.Limit = 20
	}

	resp, err := h.mediaFolderService.List(c.Request.Context(), tenantID, req)
	if err != nil {
		errResp := internal.GetErrorResponse(err)
		response.Error(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode)
		return
	}

	response.Success(c, resp, nil)
}

// updateFolder cập nhật thông tin folder
func (h *Handler) updateFolder(c *gin.Context) {
	// TODO: Get tenantID from context/middleware
	tenantID := uint(1) // Temporary hardcoded value

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID không hợp lệ", "INVALID_ID")
		return
	}

	var req dto.UpdateFolderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "VALIDATION_ERROR")
		return
	}

	resp, err := h.mediaFolderService.Update(c.Request.Context(), tenantID, uint(id), req)
	if err != nil {
		errResp := internal.GetErrorResponse(err)
		response.Error(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode)
		return
	}

	response.Success(c, resp, nil)
}

// deleteFolder xóa folder
func (h *Handler) deleteFolder(c *gin.Context) {
	// TODO: Get tenantID from context/middleware
	tenantID := uint(1) // Temporary hardcoded value

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID không hợp lệ", "INVALID_ID")
		return
	}

	err = h.mediaFolderService.Delete(c.Request.Context(), tenantID, uint(id))
	if err != nil {
		errResp := internal.GetErrorResponse(err)
		response.Error(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode)
		return
	}

	response.Success(c, gin.H{"message": "Folder đã được xóa thành công"}, nil)
}
