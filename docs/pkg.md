# Tài liệu Shared Packages - `internal/pkg`

## <PERSON><PERSON><PERSON> l<PERSON>
1. [Tổng quan](#1-tổng-quan)
2. [Danh mục Packages](#2-danh-mục-packages)
3. [Package Categories](#3-package-categories)
4. [Architecture Guidelines](#4-architecture-guidelines)
5. [Integration Patterns](#5-integration-patterns)
6. [Best Practices](#6-best-practices)

---

## 1. Tổng quan

Thư mục `internal/pkg` chứa tập hợp các shared packages được sử dụng chung trong toàn bộ ứng dụng wnapi. Các packages này cung cấp:

- **Tính nhất quán**: <PERSON><PERSON><PERSON> bảo cách xử lý lỗi, response, logging thống nhất
- **Tái sử dụng**: Tránh duplicate code giữa các modules
- **Maintainability**: Tập trung logic chung để dễ bảo trì
- **Standardization**: Thi<PERSON><PERSON> lập chuẩn chung cho toàn bộ hệ thống

### <PERSON><PERSON>ên tắc thiết kế
- **Interface-first**: Ưu tiên định nghĩa interfaces trước implementations
- **Dependency injection**: Hỗ trợ google/wire và DI patterns
- **Multi-tenant ready**: Tích hợp sẵn hỗ trợ multi-tenant architecture
- **Error handling**: Xử lý lỗi chuẩn hóa và nhất quán

---

## 2. Danh mục Packages

### 2.1 Response Package (`internal/pkg/response`)

**Import path**: `wnapi/internal/pkg/response`

**Mục đích**: Chuẩn hóa format response HTTP cho toàn bộ API

#### Key Types:
```go
type Response struct {
    Status Status      `json:"status"`
    Data   interface{} `json:"data"`
    Meta   *Meta       `json:"meta,omitempty"`
}

type Status struct {
    Code      int         `json:"code"`
    Message   string      `json:"message"`
    Success   bool        `json:"success"`
    ErrorCode string      `json:"error_code"`
    Path      string      `json:"path"`
    Timestamp string      `json:"timestamp"`
    Details   interface{} `json:"details"`
}

type Meta struct {
    NextCursor string `json:"next_cursor,omitempty"`
    HasMore    bool   `json:"has_more,omitempty"`
}
```

#### Key Functions:
```go
// Success responses
func Success(c *gin.Context, data interface{}, meta *Meta)

// Error responses
func Error(c *gin.Context, code int, message, errorCode string)
func BadRequest(c *gin.Context, message, errorCode string, details interface{})
func Unauthorized(c *gin.Context, message string)
func Forbidden(c *gin.Context, message string)
func NotFound(c *gin.Context, message string)
func InternalServerError(c *gin.Context, message string)
func ValidationError(c *gin.Context, details []Detail)
func ErrorWithDetails(c *gin.Context, code int, message, errorCode string, details interface{})
```

#### Usage Example:
```go
// Success response
response.Success(c, userData, &response.Meta{
    NextCursor: "eyJpZCI6MTIzfQ==",
    HasMore:    true,
})

// Error response
response.BadRequest(c, "Invalid input", "VALIDATION_ERROR", []response.Detail{
    {Field: "email", Message: "Email is required"},
})
```

### 2.2 Errors Package (`internal/pkg/errors`)

**Import path**: `wnapi/internal/pkg/errors`

**Mục đích**: Quản lý lỗi chuẩn hóa với error codes và HTTP status mapping

#### Key Types:
```go
type ErrorCode string
type APIError struct {
    Code    ErrorCode   `json:"code"`
    Message string      `json:"message"`
    Details interface{} `json:"details,omitempty"`
}
```

#### Predefined Error Codes:
```go
const (
    AuthenticationRequired ErrorCode = "AUTHENTICATION_REQUIRED"
    InvalidCredentials     ErrorCode = "INVALID_CREDENTIALS"
    AccessDenied          ErrorCode = "ACCESS_DENIED"
    ResourceNotFound      ErrorCode = "RESOURCE_NOT_FOUND"
    ValidationError       ErrorCode = "VALIDATION_ERROR"
    RateLimitExceeded     ErrorCode = "RATE_LIMIT_EXCEEDED"
    InternalServerError   ErrorCode = "INTERNAL_SERVER_ERROR"
    DuplicateEntry        ErrorCode = "DUPLICATE_ENTRY"
)
```

#### Usage Example:
```go
// Create API error
err := errors.New(errors.ValidationError, "Invalid email format", nil)

// With details
fieldErrors := map[string][]string{
    "email": {"Email is required", "Invalid format"},
}
err := errors.NewValidationError(fieldErrors, nil)

// Get HTTP status
status := errors.GetHTTPStatus(err) // Returns 422
```

### 2.3 Auth Package (`internal/pkg/auth`)

**Import path**: `wnapi/internal/pkg/auth`

**Mục đích**: Xử lý authentication, JWT tokens và context management

#### Key Types:
```go
type Claims struct {
    UserID   int    `json:"user_id"`
    Username string `json:"username"`
    Email    string `json:"email"`
    TenantID int    `json:"tenant_id,omitempty"`
}

type CustomClaims struct {
    UserID    uint      `json:"user_id"`
    TenantID  uint      `json:"tenant_id"`
    Email     string    `json:"email"`
    Role      string    `json:"role"`
    TokenType TokenType `json:"token_type"`
    jwt.StandardClaims
}

type JWTService struct {
    config JWTConfig
}
```

#### Key Functions:
```go
// Context helpers
func GetUserID(c *gin.Context) *uint
func GetTenantID(c *gin.Context) uint
func GetUserIDFromContext(c *gin.Context) (uint, bool)
func GetTenantIDFromContext(c *gin.Context) (uint, error)
func GetClaimsFromContext(c *gin.Context) (*CustomClaims, bool)

// JWT operations
func (s *JWTService) GenerateTokenPair(userID uint, tenantID uint, email, role string) (accessToken string, refreshToken string, err error)
func (s *JWTService) ValidateAccessToken(tokenString string) (*CustomClaims, error)
func (s *JWTService) JWTAuthMiddleware() gin.HandlerFunc

// Password utilities
func HashPassword(password string) (string, error)
func CheckPasswordHash(password, hash string) bool
```

#### Usage Example:
```go
// In middleware
jwtService := auth.NewJWTService(config)
router.Use(jwtService.JWTAuthMiddleware())

// In handler
userID, exists := auth.GetUserIDFromContext(c)
if !exists {
    response.Unauthorized(c, "Authentication required")
    return
}

tenantID := auth.GetTenantID(c)
```

### 2.4 Logger Package (`internal/pkg/logger`)

**Import path**: `wnapi/internal/pkg/logger`

**Mục đích**: Cung cấp logging interface và implementation thống nhất

#### Key Types:
```go
type Level int

const (
    LevelDebug Level = iota
    LevelInfo
    LevelWarn
    LevelError
    LevelFatal
)

type Logger interface {
    Debug(msg string, keysAndValues ...interface{})
    Info(msg string, keysAndValues ...interface{})
    Warn(msg string, keysAndValues ...interface{})
    Error(msg string, keysAndValues ...interface{})
    Fatal(msg string, keysAndValues ...interface{})
}

type ConsoleLogger struct {
    name      string
    level     Level
    logger    *log.Logger
    withColor bool
}
```

#### Key Functions:
```go
func NewConsoleLogger(name string, level Level) *ConsoleLogger
func NewConsoleLoggerWithWriter(name string, level Level, writer io.Writer) *ConsoleLogger

// Helper functions for structured logging
func String(key string, value string) interface{}
func Int(key string, value int) interface{}
```

#### Usage Example:
```go
// Create logger
logger := logger.NewConsoleLogger("wnapi", logger.LevelInfo)

// Structured logging
logger.Info("User authenticated",
    "user_id", 123,
    "tenant_id", 456,
    "email", "<EMAIL>")

logger.Error("Database connection failed",
    "error", err.Error(),
    "retry_count", 3)
```

### 2.5 Pagination Package (`internal/pkg/pagination`)

**Import path**: `wnapi/internal/pkg/pagination`

**Mục đích**: Hỗ trợ cursor-based pagination cho API responses

#### Key Types:
```go
type Params struct {
    Cursor string
    Limit  int
}

type CursorInfo struct {
    NextCursor string `json:"next_cursor"`
    HasMore    bool   `json:"has_more"`
}

type Cursor struct {
    ID int `json:"id"`
}
```

#### Key Functions:
```go
func ParseFromRequest(c *gin.Context) Params
func EncodeCursor(value string) string
func DecodeCursor(cursor string) (string, error)
func CreateNextCursor(results []interface{}, idExtractor func(interface{}) string) string
```

#### Usage Example:
```go
// In handler
params := pagination.ParseFromRequest(c)

// Query with pagination
users, err := userService.GetUsers(ctx, params.Cursor, params.Limit)

// Create response with pagination info
nextCursor := pagination.CreateNextCursor(users, func(item interface{}) string {
    user := item.(*models.User)
    return strconv.Itoa(int(user.ID))
})

meta := &response.Meta{
    NextCursor: nextCursor,
    HasMore:    len(users) == params.Limit,
}

response.Success(c, users, meta)
```

### 2.6 Tracing Package (`internal/pkg/tracing`)

**Import path**: `wnapi/internal/pkg/tracing`

**Mục đích**: OpenTelemetry tracing integration cho observability

#### Key Features:
- HTTP request tracing
- Database operation tracing
- Custom span creation
- Multiple exporters (Console, OTLP, Jaeger)
- Automatic context propagation

#### Key Functions:
```go
// Provider management
func NewProvider(config *Config) (*Provider, error)
func (p *Provider) Shutdown(ctx context.Context) error

// Span operations
func StartSpan(ctx context.Context, serviceName, operationName string) (context.Context, trace.Span)
func WithSpan(ctx context.Context, serviceName, operationName string, fn func(context.Context) error) error

// Middleware
func HTTPMiddleware(serviceName string) func(http.Handler) http.Handler
func GinMiddleware(serviceName string) gin.HandlerFunc
func DatabaseMiddleware(ctx context.Context, operation, statement string) (context.Context, trace.Span)

// Utilities
func TraceID(ctx context.Context) string
func SpanID(ctx context.Context) string
func AddSpanAttributes(ctx context.Context, attrs ...attribute.KeyValue)
func RecordError(ctx context.Context, err error)
```

#### Usage Example:
```go
// Initialize tracing
config := tracing.DefaultConfig()
provider, err := tracing.NewProvider(config)
defer provider.Shutdown(context.Background())

// HTTP middleware
router.Use(tracing.GinMiddleware("wnapi"))

// Manual spans
ctx, span := tracing.StartSpan(ctx, "user-service", "authenticate_user")
defer span.End()

tracing.AddSpanAttributes(ctx,
    attribute.String("user.email", email),
    attribute.Int("tenant.id", tenantID),
)
```

---

## 3. Package Categories

### 3.1 Infrastructure Packages
- **`response`**: HTTP response standardization
- **`errors`**: Error handling and codes
- **`logger`**: Logging infrastructure
- **`tracing`**: Observability and monitoring

### 3.2 Authentication & Authorization
- **`auth`**: JWT handling, context management, password utilities

### 3.3 Utilities
- **`pagination`**: Cursor-based pagination support

### 3.4 Future Packages (Planned)
- **`config`**: Configuration management
- **`cache`**: Caching abstraction
- **`database`**: Database connection and utilities
- **`validation`**: Input validation helpers
- **`middleware`**: Common middleware functions

---

## 4. Architecture Guidelines

### 4.1 Khi nào tạo Package mới trong `internal/pkg`

Tạo package mới khi:
- **Shared Logic**: Code được sử dụng bởi 2+ modules
- **Cross-cutting Concerns**: Logging, tracing, error handling
- **Infrastructure**: Database, cache, external services
- **Standards**: Response format, validation rules

Không tạo package khi:
- **Domain-specific**: Logic chỉ thuộc về 1 module
- **Business Logic**: Rules cụ thể của business domain
- **Module Internal**: Utilities chỉ dùng trong 1 module

### 4.2 Naming Conventions

#### Package Names:
- Sử dụng **singular nouns**: `auth`, `logger`, `response`
- **Descriptive**: Tên phải mô tả rõ chức năng
- **Short**: Tránh tên quá dài, ưu tiên 1-2 từ

#### File Names:
- **Snake_case**: `http_response.go`, `error_code.go`
- **Descriptive**: Tên file phản ánh nội dung chính
- **Grouped**: Nhóm related functions trong cùng file

#### Interface Names:
- **Verb-based**: `Logger`, `Validator`, `Authenticator`
- **-er suffix**: Theo Go convention
- **Clear contracts**: Interface phải rõ ràng về responsibility

### 4.3 Interface Design Principles

#### Single Responsibility:
```go
// Good: Focused interface
type Logger interface {
    Debug(msg string, keysAndValues ...interface{})
    Info(msg string, keysAndValues ...interface{})
    Warn(msg string, keysAndValues ...interface{})
    Error(msg string, keysAndValues ...interface{})
    Fatal(msg string, keysAndValues ...interface{})
}

// Bad: Mixed responsibilities
type LoggerAndValidator interface {
    Log(msg string)
    Validate(data interface{}) error
}
```

#### Minimal Interfaces:
```go
// Good: Small, focused interface
type UserIDExtractor interface {
    GetUserID(ctx context.Context) (uint, error)
}

// Better: Even smaller
type ContextReader interface {
    Get(key string) (interface{}, bool)
}
```

### 4.4 Dependency Management

#### Import Guidelines:
```go
// Good: Clear import grouping
import (
    // Standard library
    "context"
    "fmt"
    "net/http"

    // Third-party
    "github.com/gin-gonic/gin"
    "github.com/golang-jwt/jwt"

    // Internal shared
    "wnapi/internal/pkg/errors"
    "wnapi/internal/pkg/response"

    // Internal modules
    "wnapi/modules/auth/models"
)
```

#### Circular Dependencies:
- **Avoid**: Packages trong `internal/pkg` không được import modules
- **Use Interfaces**: Để break circular dependencies
- **Dependency Injection**: Sử dụng DI để inject implementations

---

## 5. Integration Patterns

### 5.1 Module Integration

#### Standard Pattern:
```go
// In module handler
package handler

import (
    "wnapi/internal/pkg/auth"
    "wnapi/internal/pkg/response"
    "wnapi/internal/pkg/logger"
)

type UserHandler struct {
    service UserService
    logger  logger.Logger
}

func (h *UserHandler) GetUser(c *gin.Context) {
    // Extract auth context
    userID, exists := auth.GetUserIDFromContext(c)
    if !exists {
        response.Unauthorized(c, "Authentication required")
        return
    }

    // Business logic
    user, err := h.service.GetUser(c.Request.Context(), userID)
    if err != nil {
        h.logger.Error("Failed to get user", "error", err, "user_id", userID)
        response.InternalServerError(c, "Failed to retrieve user")
        return
    }

    // Success response
    response.Success(c, user, nil)
}
```

### 5.2 Dependency Injection với Google Wire

#### Wire Provider Pattern:
```go
// In module wire.go
package di

import (
    "github.com/google/wire"
    "wnapi/internal/pkg/logger"
    "wnapi/internal/pkg/auth"
)

// ModuleProviderSet định nghĩa providers cho module
var ModuleProviderSet = wire.NewSet(
    // Shared package providers
    logger.NewConsoleLogger,
    auth.NewJWTService,

    // Module-specific providers
    NewUserService,
    NewUserHandler,

    // Bind interfaces
    wire.Bind(new(logger.Logger), new(*logger.ConsoleLogger)),
)

// InitializeUserHandler tạo UserHandler với dependencies
func InitializeUserHandler() (*UserHandler, error) {
    wire.Build(ModuleProviderSet)
    return nil, nil
}
```

### 5.3 Error Handling Pattern

#### Consistent Error Responses:
```go
// In service layer
func (s *UserService) GetUser(ctx context.Context, id uint) (*User, error) {
    user, err := s.repo.GetByID(ctx, id)
    if err != nil {
        if errors.Is(err, sql.ErrNoRows) {
            return nil, errors.New(errors.ResourceNotFound, "User not found", err)
        }
        return nil, errors.New(errors.InternalServerError, "Database error", err)
    }
    return user, nil
}

// In handler layer
func (h *UserHandler) GetUser(c *gin.Context) {
    user, err := h.service.GetUser(c.Request.Context(), userID)
    if err != nil {
        var apiErr *errors.APIError
        if errors.As(err, &apiErr) {
            status := errors.GetHTTPStatus(apiErr)
            response.ErrorWithDetails(c, status, apiErr.Message, string(apiErr.Code), apiErr.Details)
            return
        }
        response.InternalServerError(c, "Internal server error")
        return
    }
    response.Success(c, user, nil)
}
```

### 5.4 Multi-tenant Integration

#### Tenant-aware Operations:
```go
// Service với tenant support
func (s *UserService) GetUsersByTenant(ctx context.Context, tenantID uint, params pagination.Params) ([]*User, error) {
    // Validate tenant access
    if tenantID == 0 {
        return nil, errors.New(errors.ValidationError, "Tenant ID required", nil)
    }

    return s.repo.GetByTenant(ctx, tenantID, params.Cursor, params.Limit)
}

// Handler với tenant extraction
func (h *UserHandler) ListUsers(c *gin.Context) {
    tenantID := auth.GetTenantID(c)
    if tenantID == 0 {
        response.BadRequest(c, "Invalid tenant", "TENANT_REQUIRED", nil)
        return
    }

    params := pagination.ParseFromRequest(c)
    users, err := h.service.GetUsersByTenant(c.Request.Context(), tenantID, params)
    // ... handle response
}
```

---

## 6. Best Practices

### 6.1 Package Design

#### Do's:
- **Single Purpose**: Mỗi package có một mục đích rõ ràng
- **Interface First**: Định nghĩa interfaces trước implementations
- **Minimal Dependencies**: Giảm thiểu dependencies giữa packages
- **Consistent Naming**: Sử dụng naming conventions thống nhất
- **Documentation**: Viết godoc cho public APIs

#### Don'ts:
- **Circular Dependencies**: Tránh import lẫn nhau giữa packages
- **God Packages**: Tránh packages quá lớn với nhiều responsibilities
- **Implementation Details**: Không expose internal implementation
- **Breaking Changes**: Tránh thay đổi public APIs không cần thiết

### 6.2 Error Handling

#### Structured Error Handling:
```go
// Good: Structured error with context
func ValidateUser(user *User) error {
    fieldErrors := make(map[string][]string)

    if user.Email == "" {
        fieldErrors["email"] = append(fieldErrors["email"], "Email is required")
    }

    if len(fieldErrors) > 0 {
        return errors.NewValidationError(fieldErrors, nil)
    }

    return nil
}

// Good: Error wrapping with context
func (s *UserService) CreateUser(ctx context.Context, req *CreateUserRequest) error {
    if err := ValidateUser(req.User); err != nil {
        return fmt.Errorf("validation failed: %w", err)
    }

    if err := s.repo.Create(ctx, req.User); err != nil {
        return errors.New(errors.InternalServerError, "Failed to create user", err)
    }

    return nil
}
```

### 6.3 Logging Best Practices

#### Structured Logging:
```go
// Good: Structured logging with context
logger.Info("User created successfully",
    "user_id", user.ID,
    "tenant_id", user.TenantID,
    "email", user.Email,
    "created_at", user.CreatedAt,
)

// Good: Error logging with details
logger.Error("Database connection failed",
    "error", err.Error(),
    "database", "users",
    "operation", "create",
    "retry_count", retryCount,
)

// Bad: Unstructured logging
logger.Info(fmt.Sprintf("User %d created for tenant %d", user.ID, user.TenantID))
```

### 6.4 Testing Shared Packages

#### Interface Mocking:
```go
// Mock implementation for testing
type MockLogger struct {
    logs []LogEntry
}

func (m *MockLogger) Info(msg string, keysAndValues ...interface{}) {
    m.logs = append(m.logs, LogEntry{Level: "INFO", Message: msg, Data: keysAndValues})
}

// Test with mock
func TestUserHandler_GetUser(t *testing.T) {
    mockLogger := &MockLogger{}
    handler := &UserHandler{logger: mockLogger}

    // Test logic...

    // Verify logging
    assert.Len(t, mockLogger.logs, 1)
    assert.Equal(t, "User retrieved", mockLogger.logs[0].Message)
}
```

### 6.5 Performance Considerations

#### Efficient Resource Usage:
```go
// Good: Reuse logger instances
var globalLogger = logger.NewConsoleLogger("wnapi", logger.LevelInfo)

// Good: Pool expensive resources
var jwtServicePool = sync.Pool{
    New: func() interface{} {
        return auth.NewJWTService(config)
    },
}

// Good: Context-aware operations
func (s *Service) ProcessWithTimeout(ctx context.Context) error {
    ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
    defer cancel()

    return s.process(ctx)
}
```

### 6.6 Security Best Practices

#### Secure Defaults:
```go
// Good: Secure JWT configuration
config := auth.JWTConfig{
    AccessTokenExpiration:  15 * time.Minute,  // Short-lived access tokens
    RefreshTokenExpiration: 7 * 24 * time.Hour, // Longer refresh tokens
    Issuer:                "wnapi",
}

// Good: Input sanitization
func SanitizeInput(input string) string {
    // Remove dangerous characters
    return strings.ReplaceAll(input, "<script>", "")
}

// Good: Rate limiting
func (h *Handler) WithRateLimit(limit int) gin.HandlerFunc {
    return gin.HandlerFunc(func(c *gin.Context) {
        // Implement rate limiting logic
        c.Next()
    })
}
```

---

## Kết luận

Package `internal/pkg` là nền tảng quan trọng của wnapi, cung cấp các shared utilities và infrastructure components. Việc tuân thủ các guidelines và best practices trong tài liệu này sẽ giúp:

- **Maintainability**: Code dễ bảo trì và mở rộng
- **Consistency**: Cách xử lý thống nhất trong toàn bộ hệ thống
- **Reusability**: Tái sử dụng code hiệu quả
- **Testability**: Dễ dàng viết và maintain tests
- **Performance**: Tối ưu hiệu năng và resource usage

Khi phát triển modules mới, hãy luôn kiểm tra xem có thể sử dụng các shared packages hiện có trước khi tạo implementation riêng.