Tuyệt vời! Bạn muốn một tài liệu hoàn chỉnh từ A-Z, không có phần "Giữ nguyên". Điều này sẽ làm cho tài liệu trở nên độc lập và dễ theo dõi hơn. Dưới đây là phiên bản được viết lại hoàn toàn, tích hợp `google/wire` và giải thích chi tiết từng phần.

---

# Hệ thống Phân quyền RBAC - Hướng dẫn Tích hợp với `google/wire` (wnapi)

## Mục lục
1.  [Tổng quan Hệ thống Phân quyền](#1-tổng-quan-hệ-thống-phân-quyền)
    1.1. [Kiến trúc RBAC](#11-kiến-trúc-rbac)
    1.2. [Thứ tự Middleware Quan trọng](#12-thứ-tự-middleware-quan-trọng)
2.  [Quy tắc Đặt tên Permission](#2-quy-tắc-đặt-tên-permission)
    2.1. [<PERSON><PERSON><PERSON> dạng chuẩn](#21-định-dạng-chuẩn)
    2.2. [Các `action` chuẩn](#22-các-action-chuẩn)
    2.3. [Ví dụ đặt tên](#23-ví-dụ-đặt-tên)
3.  [Shared Permission Package: `internal/pkg/permission/`](#3-shared-permission-package-internalpkgpermission)
    3.1. [Cấu trúc Package](#31-cấu-trúc-package)
    3.2. [Interfaces và Types chính (`interfaces.go`)](#32-interfaces-và-types-chính-interfacesgo)
    3.3. [Constants và PermissionBuilder (`constants.go`)](#33-constants-và-permissionbuilder-constantsgo)
    3.4. [Xử lý Lỗi Chuẩn hóa (`errors.go`)](#34-xử-lý-lỗi-chuẩn-hóa-errorsgo)
    3.5. [Factory Tạo Middleware (`factory.go`)](#35-factory-tạo-middleware-factorygo)
    3.6. [Caching Permission (`cache.go`)](#36-caching-permission-cachego)
    3.7. [Định nghĩa Providers và Injectors với `google/wire` (`permission/wire.go`)](#37-định-nghĩa-providers-và-injectors-với-googlewire-permissionwirego)
4.  [Tích hợp `google/wire` vào Ứng dụng](#4-tích-hợp-googlewire-vào-ứng-dụng)
    4.1. [Thiết lập `wire` cho Module RBAC (Cung cấp `PermissionChecker`)](#41-thiết-lập-wire-cho-module-rbac-cung-cấp-permissionchecker)
    4.2. [Khởi tạo Ứng dụng với `wire` (Injector Chính)](#42-khởi-tạo-ứng-dụng-với-wire-injector-chính)
    4.3. [Cấu trúc `core.App` và cách nhận Dependencies từ `wire`](#43-cấu-trúc-coreapp-và-cách-nhận-dependencies-từ-wire)
5.  [Tích hợp vào Modules Chức năng](#5-tích-hợp-vào-modules-chức-năng)
    5.1. [Thiết lập `wire` cho Module Chức năng](#51-thiết-lập-wire-cho-module-chức-năng)
    5.2. [Sử dụng `MiddlewareFactory` trong Routes của Module](#52-sử-dụng-middlewarefactory-trong-routes-của-module)
6.  [Chạy `wire` để Generate Code](#6-chạy-wire-để-generate-code)
7.  [Benefits và Usage Patterns](#7-benefits-và-usage-patterns)
    7.1. [Lợi ích của Shared Package và `wire`](#71-lợi-ích-của-shared-package-và-wire)
    7.2. [Usage Patterns của Middleware](#72-usage-patterns-của-middleware)
8.  [Migration Guide (Từ không `wire` sang có `wire`)](#8-migration-guide-từ-không-wire-sang-có-wire)
9.  [Best Practices](#9-best-practices)
    9.1. [Nguyên tắc Bảo mật](#91-nguyên-tắc-bảo-mật)
    9.2. [Performance Optimization](#92-performance-optimization)
    9.3. [Error Handling](#93-error-handling)
    9.4. [Testing](#94-testing)
10. [Troubleshooting](#10-troubleshooting)
    10.1. [Các lỗi thường gặp](#101-các-lỗi-thường-gặp)
    10.2. [Debug Tips](#102-debug-tips)
    10.3. [Lỗi liên quan đến `wire`](#103-lỗi-liên-quan-đến-wire)

---

## 1. Tổng quan Hệ thống Phân quyền

### 1.1 Kiến trúc RBAC
Hệ thống RBAC (Role-Based Access Control) của wnapi sử dụng mô hình phân quyền dựa trên vai trò với kiến trúc multi-tenant:

```
User ←→ UserRole ←→ Role ←→ RolePermission ←→ Permission
  ↓                                              ↓
Tenant                                    PermissionGroup
```

-   **User**: Người dùng trong hệ thống.
-   **Role**: Vai trò (ví dụ: admin, manager, user).
-   **Permission**: Quyền cụ thể (ví dụ: `products.create`, `users.read`).
-   **PermissionGroup**: Nhóm quyền để tổ chức tốt hơn (ví dụ: "Quản lý sản phẩm", "Quản lý người dùng").
-   **Tenant**: Phân vùng dữ liệu cho kiến trúc multi-tenant, đảm bảo dữ liệu của một tenant không bị truy cập bởi tenant khác.

### 1.2 Thứ tự Middleware Quan trọng
Để hệ thống phân quyền hoạt động chính xác, thứ tự áp dụng middleware cho các route cần bảo vệ là cực kỳ quan trọng:

1.  **Tenant Middleware**: Xác định `TenantID` dựa trên request (ví dụ: từ subdomain, header, hoặc JWT payload) và đưa vào context.
2.  **Authentication Middleware (Auth)**: Xác thực người dùng (ví dụ: qua JWT), lấy `UserID` và các thông tin khác, đưa vào context.
3.  **RBAC Middleware (Permission)**: Sử dụng `TenantID` và `UserID` từ context để kiểm tra quyền truy cập của người dùng đối với một hành động/resource cụ thể.

```go
// Ví dụ trong việc định nghĩa routes sử dụng Gin Gonic
// protectedRoutes là một group các API cần được bảo vệ
protectedRoutes := apiGroup.Group("")
protectedRoutes.Use(tenantMiddleware())     // 1. Xác định tenant
protectedRoutes.Use(jwtAuthMiddleware())    // 2. Xác thực người dùng
// permissionMiddleware() sẽ được cung cấp bởi MiddlewareFactory,
// được khởi tạo và inject thông qua google/wire
protectedRoutes.Use(permissionMiddleware()) // 3. Kiểm tra quyền
```

## 2. Quy tắc Đặt tên Permission

Việc đặt tên permission một cách nhất quán và có cấu trúc giúp dễ dàng quản lý và hiểu rõ phạm vi của từng quyền.

### 2.1 Định dạng chuẩn
Đề xuất định dạng: ` {module}.{action}.{resource}`

-   `{module}`: Tên module chức năng (ví dụ: `products`, `users`, `rbac`).
-   `{action}`: Hành động được thực hiện (xem mục 2.2).
-   `{resource}`: (Tùy chọn) Đối tượng cụ thể trong module mà hành động tác động đến. Nếu action áp dụng cho toàn bộ module hoặc không có resource cụ thể, phần này có thể bỏ qua hoặc module và resource có thể được gộp lại.

Nếu action không áp dụng cho một resource cụ thể trong module đó, hoặc resource đã ngầm hiểu từ module, có thể dùng:
`{module}.{action}`

### 2.2 Các `action` chuẩn
Sử dụng một bộ action chuẩn giúp thống nhất trong toàn hệ thống:
-   `create`: Tạo mới một đối tượng.
-   `read`: Đọc/xem chi tiết một đối tượng.
-   `update`: Cập nhật một đối tượng.
-   `delete`: Xóa một đối tượng.
-   `list`: Liệt kê danh sách các đối tượng.
-   `manage`: Quyền quản lý toàn diện trên module/resource (thường bao gồm tất cả các action CRUD và có thể thêm các action quản trị khác).
-   Các action đặc thù khác: `assign` (ví dụ: `rbac.user_roles.assign`), `send` (ví dụ: `notifications.send`), `approve`, `reject`, `publish`, v.v.

### 2.3 Ví dụ đặt tên
```go


// Module RBAC (ví dụ với resource rõ ràng)
"rbac.roles.create"         // Tạo vai trò (resource: roles)
"rbac.permissions.read"     // Xem quyền (resource: permissions)
"rbac.user_roles.assign"    // Gán vai trò cho người dùng (resource: user_roles)


## 3. Shared Permission Package: `internal/pkg/permission/`

Để đảm bảo tính nhất quán, tái sử dụng code, dễ bảo trì và tích hợp `google/wire` một cách hiệu quả, một package chia sẻ cho việc xử lý permission được đặt tại `internal/pkg/permission/`.

### 3.1 Cấu trúc Package
```text
internal/pkg/permission/
├── interfaces.go          # Định nghĩa interfaces chung (PermissionChecker, PermissionMiddleware)
├── errors.go             # Xử lý lỗi chuẩn hóa cho permission
├── cache.go              # Logic cache cho việc kiểm tra permission (CachedPermissionChecker)
├── constants.go          # Hằng số, action chuẩn, patterns đặt tên, PermissionBuilder
├── factory.go            # Factory để tạo các middleware kiểm tra permission (MiddlewareFactory)
├── wire.go               # Định nghĩa providers và provider sets cho google/wire
└── wire_gen.go           # File được google/wire tự động generate (không sửa thủ công)
```

### 3.2 Interfaces và Types chính (`interfaces.go`)
File này định nghĩa các hợp đồng (interfaces) mà các thành phần khác phải tuân theo.
```go
// internal/pkg/permission/interfaces.go
package permission

import (
	"context"
	"time" // Cần cho PermissionResult

	"github.com/gin-gonic/gin"
)

// PermissionChecker là interface cho việc kiểm tra quyền của người dùng.
// Implementation cụ thể của interface này thường nằm trong module RBAC,
// nơi có logic truy vấn database để xác định quyền.
type PermissionChecker interface {
	// UserHasPermission kiểm tra xem người dùng có một quyền cụ thể không.
	UserHasPermission(ctx context.Context, tenantID uint, userID uint, permissionCode string) (bool, error)

	// UserHasAnyPermission kiểm tra xem người dùng có ít nhất một trong danh sách các quyền không.
	UserHasAnyPermission(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error)

	// UserHasAllPermissions kiểm tra xem người dùng có tất cả các quyền trong danh sách không.
	UserHasAllPermissions(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error)
}

// PermissionMiddleware là interface cho việc tạo ra các Gin middleware functions
// để bảo vệ routes dựa trên quyền.
type PermissionMiddleware interface {
	RequirePermission(permission string) gin.HandlerFunc
	RequireAnyPermission(permissions ...string) gin.HandlerFunc
	RequireAllPermissions(permissions ...string) gin.HandlerFunc
}

// PermissionContext (Tùy chọn) có thể chứa thông tin context cho việc kiểm tra permission,
// hữu ích cho logging hoặc các quyết định phân quyền phức tạp hơn.
type PermissionContext struct {
	TenantID uint
	UserID   uint
	Path     string
	Method   string
}

// PermissionResult (Tùy chọn) có thể là kết quả chi tiết của việc kiểm tra quyền.
type PermissionResult struct {
	Allowed     bool
	Permission  string
	Reason      string // Lý do từ chối (nếu có)
	CheckedAt   time.Time
}
```

### 3.3 Constants và PermissionBuilder (`constants.go`)
File này chứa các hằng số và tiện ích giúp xây dựng chuỗi permission một cách nhất quán.
```go
// internal/pkg/permission/constants.go
package permission

import "fmt"

const (
	// Standard Actions
	ActionCreate = "create"
	ActionRead   = "read"
	ActionUpdate = "update"
	ActionDelete = "delete"
	ActionList   = "list"
	ActionManage = "manage"
	// Thêm các action đặc thù khác nếu cần
	ActionAssign = "assign"
	ActionSend   = "send"

	// PermissionSeparator là ký tự phân tách các phần trong chuỗi permission.
	PermissionSeparator = "."

	// Error Codes (sẽ được sử dụng trong errors.go)
	// Chúng có thể được định nghĩa ở đây để tham chiếu hoặc trực tiếp trong errors.go
	ErrorCodeAuthRequired     = "AUTHENTICATION_REQUIRED"
	ErrorCodeTenantRequired   = "TENANT_REQUIRED"
	ErrorCodePermissionDenied = "PERMISSION_DENIED"
	ErrorCodePermissionCheck  = "PERMISSION_CHECK_ERROR"
)

// PermissionBuilder là một helper struct để xây dựng chuỗi permission một cách an toàn và nhất quán.
type PermissionBuilder struct {
	module   string
	action   string
	resource string // Optional
}

// NewPermissionBuilder khởi tạo một PermissionBuilder với tên module.
func NewPermissionBuilder(module string) *PermissionBuilder {
	return &PermissionBuilder{module: module}
}

// Action thiết lập action cho permission.
func (pb *PermissionBuilder) Action(action string) *PermissionBuilder {
	pb.action = action
	return pb
}

// Resource thiết lập resource (tùy chọn) cho permission.
func (pb *PermissionBuilder) Resource(resource string) *PermissionBuilder {
	pb.resource = resource
	return pb
}

// Build tạo ra chuỗi permission hoàn chỉnh.
func (pb *PermissionBuilder) Build() string {
	if pb.module == "" || pb.action == "" {
		// Hoặc panic, hoặc trả về lỗi, hoặc log warning
		// Để đơn giản, giả sử module và action luôn được cung cấp
	}
	if pb.resource != "" {
		return fmt.Sprintf("%s%s%s%s%s", pb.module, PermissionSeparator, pb.action, PermissionSeparator, pb.resource)
	}
	return fmt.Sprintf("%s%s%s", pb.module, PermissionSeparator, pb.action)
}

// Helper functions cho các bộ quyền CRUD phổ biến
func BuildCRUDPermissions(module string, resource ...string) []string {
	resName := ""
	if len(resource) > 0 && resource[0] != "" {
		resName = resource[0]
	}

	baseBuilder := NewPermissionBuilder(module)
	if resName != "" {
		baseBuilder.Resource(resName)
	}

	return []string{
		NewPermissionBuilder(module).Action(ActionCreate).Resource(resName).Build(), // Tạo mới
		NewPermissionBuilder(module).Action(ActionRead).Resource(resName).Build(),   // Đọc
		NewPermissionBuilder(module).Action(ActionUpdate).Resource(resName).Build(), // Cập nhật
		NewPermissionBuilder(module).Action(ActionDelete).Resource(resName).Build(), // Xóa
		NewPermissionBuilder(module).Action(ActionList).Resource(resName).Build(),   // Liệt kê
	}
}

// Định nghĩa các hằng số permission cụ thể (ví dụ cho một module "example")
const (
    ExampleModule = "example" // Tên module

    ExampleCreatePermission = ExampleModule + PermissionSeparator + ActionCreate // "example.create"
    ExampleReadPermission   = ExampleModule + PermissionSeparator + ActionRead   // "example.read"
    // ... các permission khác ...
)
```
**Khuyến nghị:** Định nghĩa các hằng số permission (như `ExampleCreatePermission`) trong package của từng module (ví dụ `modules/product/permission_defs.go`) hoặc một package `definitions` tập trung để tránh "magic strings".

### 3.4 Xử lý Lỗi Chuẩn hóa (`errors.go`)
Cung cấp các hàm tiện ích để trả về response lỗi một cách nhất quán khi việc kiểm tra permission thất bại. Sử dụng package `internal/pkg/response` để đảm bảo tính nhất quán trong toàn bộ hệ thống.
```go
// internal/pkg/permission/errors.go
package permission

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"wnapi/internal/pkg/response" // Sử dụng shared response package
)

// AbortWithAuthRequired dừng request và trả về lỗi 401 yêu cầu xác thực.
func AbortWithAuthRequired(c *gin.Context) {
	response.Unauthorized(c, "Yêu cầu xác thực")
	c.Abort()
}

// AbortWithTenantRequired dừng request và trả về lỗi 401 (hoặc 400/403 tùy logic) yêu cầu tenant.
func AbortWithTenantRequired(c *gin.Context) {
	response.ErrorWithDetails(c, http.StatusUnauthorized,
		"Không tìm thấy thông tin tenant hoặc tenant không hợp lệ",
		ErrorCodeTenantRequired, nil)
	c.Abort()
}

// AbortWithPermissionDenied dừng request và trả về lỗi 403 từ chối quyền truy cập.
func AbortWithPermissionDenied(c *gin.Context, permission string) {
	var details interface{}
	if permission != "" {
		details = []response.Detail{
			{
				Field:   "required_permission",
				Message: "Bạn không có quyền thực hiện hành động này: " + permission,
			},
		}
	}
	response.ErrorWithDetails(c, http.StatusForbidden,
		"Không có quyền truy cập",
		ErrorCodePermissionDenied, details)
	c.Abort()
}

// AbortWithPermissionCheckError dừng request và trả về lỗi 500 do lỗi hệ thống khi kiểm tra quyền.
func AbortWithPermissionCheckError(c *gin.Context, err error) {
	// Log lỗi `err` ở đây nếu logger có sẵn
	response.ErrorWithDetails(c, http.StatusInternalServerError,
		"Lỗi hệ thống khi kiểm tra quyền truy cập",
		ErrorCodePermissionCheck, nil)
	c.Abort()
}
```

### 3.5 Factory Tạo Middleware (`factory.go`)
`MiddlewareFactory` chịu trách nhiệm tạo ra các `gin.HandlerFunc` để kiểm tra quyền. Nó sử dụng một `PermissionChecker` (có thể đã được cache) để thực hiện việc kiểm tra.
```go
// internal/pkg/permission/factory.go
package permission

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"wnapi/internal/pkg/auth"   // Giả định package auth cung cấp các hàm GetUserIDFromContext, GetTenantIDFromContext
	"wnapi/internal/pkg/logger" // Giả định có package logger
)

// MiddlewareFactory tạo ra các middleware functions để kiểm tra quyền.
// Nó implement PermissionMiddleware interface.
type MiddlewareFactory struct {
	checker PermissionChecker // Checker này có thể là CachedPermissionChecker
	logger  logger.Logger
}

// NewMiddlewareFactory là constructor cho MiddlewareFactory.
// Đây sẽ là một provider cho google/wire.
func NewMiddlewareFactory(checker PermissionChecker, log logger.Logger) *MiddlewareFactory {
	return &MiddlewareFactory{
		checker: checker,
		logger:  log,
	}
}

// RequirePermission tạo middleware kiểm tra một quyền cụ thể.
func (mf *MiddlewareFactory) RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, tenantID, ok := mf.extractAuthContext(c)
		if !ok {
			return // Lỗi đã được xử lý trong extractAuthContext
		}

		hasPermission, err := mf.checker.UserHasPermission(c.Request.Context(), tenantID, userID, permission)
		if err != nil {
			mf.logger.ErrorContext(c.Request.Context(), "Lỗi kiểm tra quyền (UserHasPermission)",
				"error", err, "user_id", userID, "tenant_id", tenantID, "permission", permission)
			AbortWithPermissionCheckError(c, err)
			return
		}

		if !hasPermission {
			mf.logger.WarnContext(c.Request.Context(), "Từ chối quyền truy cập",
				"user_id", userID, "tenant_id", tenantID, "required_permission", permission, "path", c.Request.URL.Path)
			AbortWithPermissionDenied(c, permission)
			return
		}
		c.Next()
	}
}

// RequireAnyPermission tạo middleware kiểm tra người dùng có ít nhất một trong các quyền được liệt kê.
func (mf *MiddlewareFactory) RequireAnyPermission(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if len(permissions) == 0 { // Nếu không yêu cầu quyền nào, cho qua
			mf.logger.InfoContext(c.Request.Context(), "Không có quyền nào được yêu cầu trong RequireAnyPermission, cho qua.",
				"path", c.Request.URL.Path)
			c.Next()
			return
		}

		userID, tenantID, ok := mf.extractAuthContext(c)
		if !ok {
			return
		}

		hasAny, err := mf.checker.UserHasAnyPermission(c.Request.Context(), tenantID, userID, permissions)
		if err != nil {
			mf.logger.ErrorContext(c.Request.Context(), "Lỗi kiểm tra quyền (UserHasAnyPermission)",
				"error", err, "user_id", userID, "tenant_id", tenantID, "permissions", permissions)
			AbortWithPermissionCheckError(c, err)
			return
		}

		if !hasAny {
			permissionStr := fmt.Sprintf("any of: %v", permissions)
			mf.logger.WarnContext(c.Request.Context(), "Từ chối quyền truy cập (any of)",
				"user_id", userID, "tenant_id", tenantID, "required_permissions", permissions, "path", c.Request.URL.Path)
			AbortWithPermissionDenied(c, permissionStr)
			return
		}
		c.Next()
	}
}

// RequireAllPermissions tạo middleware kiểm tra người dùng có tất cả các quyền được liệt kê.
func (mf *MiddlewareFactory) RequireAllPermissions(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if len(permissions) == 0 { // Nếu không yêu cầu quyền nào, cho qua
			mf.logger.InfoContext(c.Request.Context(), "Không có quyền nào được yêu cầu trong RequireAllPermissions, cho qua.",
				"path", c.Request.URL.Path)
			c.Next()
			return
		}

		userID, tenantID, ok := mf.extractAuthContext(c)
		if !ok {
			return
		}

		hasAll, err := mf.checker.UserHasAllPermissions(c.Request.Context(), tenantID, userID, permissions)
		if err != nil {
			mf.logger.ErrorContext(c.Request.Context(), "Lỗi kiểm tra quyền (UserHasAllPermissions)",
				"error", err, "user_id", userID, "tenant_id", tenantID, "permissions", permissions)
			AbortWithPermissionCheckError(c, err)
			return
		}

		if !hasAll {
			permissionStr := fmt.Sprintf("all of: %v", permissions)
			mf.logger.WarnContext(c.Request.Context(), "Từ chối quyền truy cập (all of)",
				"user_id", userID, "tenant_id", tenantID, "required_permissions", permissions, "path", c.Request.URL.Path)
			AbortWithPermissionDenied(c, permissionStr)
			return
		}
		c.Next()
	}
}

// extractAuthContext là helper method để lấy UserID và TenantID từ Gin context.
// Trả về true nếu thành công, false nếu có lỗi và đã abort request.
func (mf *MiddlewareFactory) extractAuthContext(c *gin.Context) (userID uint, tenantID uint, ok bool) {
	// Lấy UserID từ context (do Auth middleware đặt vào)
	uID, exists := auth.GetUserIDFromContext(c) // Giả định hàm này trả về (uint, bool)
	if !exists {
		mf.logger.WarnContext(c.Request.Context(), "Không tìm thấy UserID trong context, yêu cầu xác thực.",
			"path", c.Request.URL.Path)
		AbortWithAuthRequired(c)
		return 0, 0, false
	}
	userID = uID

	// Lấy TenantID từ context (do Tenant middleware hoặc Auth middleware đặt vào)
	tID, err := auth.GetTenantIDFromContext(c) // Giả định hàm này trả về (uint, error)
	if err != nil {
		mf.logger.WarnContext(c.Request.Context(), "Không tìm thấy TenantID trong context hoặc lỗi lấy TenantID.",
			"error", err, "path", c.Request.URL.Path)
		AbortWithTenantRequired(c)
		return 0, 0, false
	}
	tenantID = tID

	return userID, tenantID, true
}
```

### 3.6 Caching Permission (`cache.go`)
`CachedPermissionChecker` là một decorator bao bọc một `PermissionChecker` khác (thường là implementation truy vấn DB) để thêm lớp cache, giúp cải thiện hiệu năng.
```go
// internal/pkg/permission/cache.go
package permission

import (
	"context"
	"fmt"
	"strings"
	"time"

	"wnapi/internal/pkg/cache"   // Giả định có interface cache.Cache
	"wnapi/internal/pkg/logger" // Thêm logger để ghi log cache miss/hit nếu cần
)

// CachedPermissionChecker implement PermissionChecker và thêm lớp caching.
type CachedPermissionChecker struct {
	delegate PermissionChecker // PermissionChecker thực tế (ví dụ: truy vấn DB)
	cache    cache.Cache
	ttl      time.Duration
	logger   logger.Logger     // Logger để ghi log cache operations (tùy chọn)
}

// NewCachedPermissionChecker là constructor cho CachedPermissionChecker.
// Đây sẽ là một provider cho google/wire.
// Lưu ý: `checker` ở đây là un-cached version.
func NewCachedPermissionChecker(
	checker PermissionChecker,
	c cache.Cache,
	ttl time.Duration,
	log logger.Logger,
) *CachedPermissionChecker {
	return &CachedPermissionChecker{
		delegate: checker,
		cache:    c,
		ttl:      ttl,
		logger:   log,
	}
}

func (cpc *CachedPermissionChecker) buildCacheKey(tenantID uint, userID uint, permissionCode string) string {
	// Chuẩn hóa permissionCode để tránh các vấn đề về key (ví dụ: khoảng trắng, ký tự đặc biệt)
	normalizedPermissionCode := strings.ReplaceAll(permissionCode, " ", "_")
	normalizedPermissionCode = strings.ToLower(normalizedPermissionCode)
	return fmt.Sprintf("perm_cache:%d:%d:%s", tenantID, userID, normalizedPermissionCode)
}

// UserHasPermission kiểm tra quyền, ưu tiên lấy từ cache.
func (cpc *CachedPermissionChecker) UserHasPermission(ctx context.Context, tenantID uint, userID uint, permissionCode string) (bool, error) {
	cacheKey := cpc.buildCacheKey(tenantID, userID, permissionCode)

	// 1. Kiểm tra cache trước
	var cachedResult bool
	// Giả sử cache.Get trả về (interface{}, error), cần type assertion
	if val, err := cpc.cache.Get(ctx, cacheKey); err == nil {
		if bVal, ok := val.(bool); ok {
			cpc.logger.DebugContext(ctx, "Cache hit for permission",
				"key", cacheKey, "user_id", userID, "tenant_id", tenantID)
			return bVal, nil
		}
		// Log lỗi nếu type không đúng, coi như cache miss
		cpc.logger.WarnContext(ctx, "Cache data type mismatch for permission",
			"key", cacheKey, "expected_type", "bool", "actual_type", fmt.Sprintf("%T", val))
	} else {
		// Không log lỗi "not found" của cache như một error, đó là cache miss.
		// Chỉ log các lỗi thực sự của cache (ví dụ: connection error)
		if !cpc.cache.IsErrCacheMiss(err) { // Giả sử có hàm IsErrCacheMiss
			cpc.logger.ErrorContext(ctx, "Lỗi khi lấy permission từ cache",
				"key", cacheKey, "error", err, "user_id", userID, "tenant_id", tenantID)
		} else {
            cpc.logger.DebugContext(ctx, "Cache miss for permission",
				"key", cacheKey, "user_id", userID, "tenant_id", tenantID)
        }
	}


	// 2. Nếu cache miss hoặc lỗi, query từ delegate (DB)
	hasPermission, err := cpc.delegate.UserHasPermission(ctx, tenantID, userID, permissionCode)
	if err != nil {
		return false, err // Trả về lỗi từ delegate
	}

	// 3. Lưu kết quả vào cache
	// Chỉ cache nếu không có lỗi từ delegate
	if errSet := cpc.cache.Set(ctx, cacheKey, hasPermission, cpc.ttl); errSet != nil {
		cpc.logger.ErrorContext(ctx, "Lỗi khi lưu permission vào cache",
			"key", cacheKey, "error", errSet, "user_id", userID, "tenant_id", tenantID)
		// Không trả về lỗi này cho client, vẫn trả về hasPermission
	}

	return hasPermission, nil
}

// UserHasAnyPermission sử dụng UserHasPermission (đã được cache) để kiểm tra.
func (cpc *CachedPermissionChecker) UserHasAnyPermission(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	if len(permissions) == 0 {
		return true, nil // Nếu không có quyền nào được yêu cầu, coi như có quyền
	}
	for _, p := range permissions {
		has, err := cpc.UserHasPermission(ctx, tenantID, userID, p) // Gọi hàm đã có cache
		if err != nil {
			// Lỗi nghiêm trọng khi kiểm tra một quyền, trả về lỗi ngay
			return false, fmt.Errorf("lỗi khi kiểm tra quyền '%s': %w", p, err)
		}
		if has {
			return true, nil
		}
	}
	return false, nil
}

// UserHasAllPermissions sử dụng UserHasPermission (đã được cache) để kiểm tra.
func (cpc *CachedPermissionChecker) UserHasAllPermissions(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	if len(permissions) == 0 {
		return true, nil // Nếu không có quyền nào được yêu cầu, coi như có quyền
	}
	for _, p := range permissions {
		has, err := cpc.UserHasPermission(ctx, tenantID, userID, p) // Gọi hàm đã có cache
		if err != nil {
			// Lỗi nghiêm trọng khi kiểm tra một quyền, trả về lỗi ngay
			return false, fmt.Errorf("lỗi khi kiểm tra quyền '%s': %w", p, err)
		}
		if !has {
			return false, nil
		}
	}
	return true, nil
}

// InvalidateUserPermissions xóa tất cả các cache permission liên quan đến một user.
// CẢNH BÁO: Xóa theo pattern có thể ảnh hưởng performance trên một số hệ thống cache (ví dụ Redis với KEYS).
// Cân nhắc các chiến lược cache invalidation khác nếu cần.
func (cpc *CachedPermissionChecker) InvalidateUserPermissions(ctx context.Context, tenantID uint, userID uint) error {
	// Pattern này cần được hỗ trợ bởi implementation của cache.Cache
	pattern := fmt.Sprintf("perm_cache:%d:%d:*", tenantID, userID)
	cpc.logger.InfoContext(ctx, "Invalidating user permissions cache",
		"user_id", userID, "tenant_id", tenantID, "pattern", pattern)
	return cpc.cache.DeletePattern(ctx, pattern) // Giả sử DeletePattern tồn tại
}
```

### 3.7 Định nghĩa Providers và Injectors với `google/wire` (`permission/wire.go`)
File này khai báo các "công thức" để `google/wire` biết cách tạo ra các thành phần của package `permission`.

```go
// internal/pkg/permission/wire.go
//go:build wireinject
// +build wireinject

package permission

import (
	"time"

	"github.com/google/wire"
	"wnapi/internal/pkg/cache"   // Interface cache.Cache và provider của nó phải có sẵn
	"wnapi/internal/pkg/config"  // Interface config.Config và provider của nó (để lấy TTL)
	"wnapi/internal/pkg/logger"  // Interface logger.Logger và provider của nó
	// Interface PermissionChecker (un-cached) sẽ được cung cấp bởi module RBAC
	// Chúng ta không import trực tiếp implementation của RBAC ở đây, mà sẽ bind interface.
)

// PermissionPkgSet là một Wire provider set bao gồm tất cả các providers
// cần thiết để tạo ra các thành phần của package permission.
// Nó mong đợi một PermissionChecker (un-cached version) và Logger được cung cấp từ bên ngoài.
var PermissionPkgSet = wire.NewSet(
	NewMiddlewareFactory,
	NewCachedPermissionChecker,
	providePermissionCacheTTL, // Cung cấp giá trị TTL cho cache permission

	// Chúng ta bind PermissionMiddleware interface với implementation là *MiddlewareFactory
	// Wire thường tự động suy luận điều này nếu *MiddlewareFactory implement PermissionMiddleware.
	// wire.Bind(new(PermissionMiddleware), new(*MiddlewareFactory)), // Có thể không cần nếu wire tự suy luận

	// Tương tự, CachedPermissionChecker cũng là một PermissionChecker.
	// Khi MiddlewareFactory yêu cầu PermissionChecker, Wire sẽ ưu tiên cung cấp
	// *CachedPermissionChecker nếu nó được khai báo trong cùng set hoặc một set được build cùng.
)

// providePermissionCacheTTL là một provider đơn giản cho cache TTL của permission.
// Nó lấy giá trị từ config.
func providePermissionCacheTTL(cfg config.Config) time.Duration { // Giả định config.Config có method GetDuration
	// Ví dụ: return cfg.GetDuration("cache.permission.ttl")
	// Hoặc một giá trị mặc định nếu không có trong config
	return cfg.GetDurationWithDefault("cache.permission.ttl", 5*time.Minute)
}


// Các injectors dưới đây chỉ mang tính minh họa cách Wire có thể tạo ra các thành phần.
// Trong thực tế, injector chính sẽ nằm ở `cmd/server/wire.go` (hoặc tương tự)
// và sẽ build toàn bộ ứng dụng hoặc các service lớn.

// ---- Illustrative Injectors (Không sử dụng trực tiếp, chỉ để `wire` hiểu) ----

// InitializeMiddlewareFactoryExample minh họa cách Wire tạo MiddlewareFactory.
// `actualChecker` là PermissionChecker không cache (ví dụ, từ module RBAC).
func InitializeMiddlewareFactoryExample(
	log logger.Logger,
	appCache cache.Cache,
	cfg config.Config,
	actualChecker PermissionChecker, // Đây là dependency quan trọng từ module RBAC
) (*MiddlewareFactory, error) {
	wire.Build(
        PermissionPkgSet, // Sử dụng set đã định nghĩa
        // Các providers cho logger, cache, config, và actualChecker phải có sẵn
        // trong scope của injector lớn hơn (ví dụ: AppSet).
        // Ở đây, chúng ta giả sử actualChecker được truyền vào trực tiếp.
	)
	return nil, nil // Wire sẽ generate implementation
}

// InitializeCachedCheckerExample minh họa cách Wire tạo CachedPermissionChecker.
func InitializeCachedCheckerExample(
    log logger.Logger,
    appCache cache.Cache,
    cfg config.Config,
    actualChecker PermissionChecker,
) (*CachedPermissionChecker, error) {
    wire.Build(
        NewCachedPermissionChecker, // Provider trực tiếp
        providePermissionCacheTTL,  // Provider cho TTL
        // Các providers cho log, appCache, actualChecker phải có sẵn.
    )
    return nil, nil
}
```
**Lưu ý:**
*   `PermissionPkgSet` không bao gồm provider cho `PermissionChecker` (un-cached version). Provider này phải đến từ module RBAC (module chịu trách nhiệm kiểm tra quyền thực tế).
*   `providePermissionCacheTTL` giờ đây lấy `config.Config` làm dependency, giúp cấu hình TTL linh hoạt hơn.

## 4. Tích hợp `google/wire` vào Ứng dụng

### 4.1. Thiết lập `wire` cho Module RBAC (Cung cấp `PermissionChecker`)
Module RBAC (ví dụ: `modules/rbac/`) chịu trách nhiệm triển khai logic kiểm tra quyền thực tế (thường là truy vấn cơ sở dữ liệu). Nó cần cung cấp một implementation của `permission.PermissionChecker`.

**Ví dụ: `modules/rbac/internal/service/permission_service.go`**
(Đây là service chứa logic kiểm tra quyền, implement `permission.PermissionChecker`)
```go
package service

import (
	"context"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission" // Import interface từ shared package
	// Import các repository của RBAC
	"wnapi/modules/rbac/internal/repository"
)

type PermissionService struct {
	userRoleRepo repository.UserRoleRepository
	rolePermRepo repository.RolePermissionRepository
	// ... các repo khác nếu cần
	logger logger.Logger
}

// NewRBACPermissionService là constructor cho PermissionService.
// Đây sẽ là provider cho google/wire trong module RBAC.
func NewRBACPermissionService(
	userRoleRepo repository.UserRoleRepository,
	rolePermRepo repository.RolePermissionRepository,
	log logger.Logger,
) *PermissionService { // Trả về concrete type
	return &PermissionService{
		userRoleRepo: userRoleRepo,
		rolePermRepo: rolePermRepo,
		logger:       log,
	}
}

// UserHasPermission implement permission.PermissionChecker
func (s *PermissionService) UserHasPermission(ctx context.Context, tenantID uint, userID uint, permissionCode string) (bool, error) {
	// TODO: Implement logic kiểm tra quyền thực tế dựa trên userID, tenantID và permissionCode
	// Ví dụ:
	// 1. Lấy tất cả roles của user trong tenant.
	// 2. Với mỗi role, lấy tất cả permissions của role đó.
	// 3. Kiểm tra xem permissionCode có trong danh sách không, hoặc có quyền 'manage' tương ứng không.
	s.logger.DebugContext(ctx, "RBACService: Checking permission",
		"tenant_id", tenantID, "user_id", userID, "permission", permissionCode)
	// Đây là logic giả định
	if userID == 1 && tenantID == 1 && (permissionCode == "products.create" || permissionCode == "products.manage") {
		return true, nil
	}
	return false, nil // Giả sử mặc định là không có quyền
}

// UserHasAnyPermission implement permission.PermissionChecker
func (s *PermissionService) UserHasAnyPermission(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	// TODO: Implement logic tối ưu hơn nếu có thể.
	// Cách đơn giản là lặp và gọi UserHasPermission.
	for _, pCode := range permissions {
		has, err := s.UserHasPermission(ctx, tenantID, userID, pCode)
		if err != nil {
			return false, err // Propagate error
		}
		if has {
			return true, nil
		}
	}
	return false, nil
}

// UserHasAllPermissions implement permission.PermissionChecker
func (s *PermissionService) UserHasAllPermissions(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	// TODO: Implement logic tối ưu hơn nếu có thể.
	for _, pCode := range permissions {
		has, err := s.UserHasPermission(ctx, tenantID, userID, pCode)
		if err != nil {
			return false, err // Propagate error
		}
		if !has {
			return false, nil
		}
	}
	return true, nil
}
```

**Ví dụ: `modules/rbac/internal/di/wire.go`**
(File `wire.go` cho module RBAC)
```go
// modules/rbac/internal/di/wire.go
//go:build wireinject
// +build wireinject

package di // Hoặc tên package phù hợp trong module RBAC

import (
	"github.com/google/wire"
	"wnapi/internal/pkg/database" // Giả định có DBManager và provider của nó
	"wnapi/internal/pkg/logger"
	sharedPerm "wnapi/internal/pkg/permission" // Alias cho shared permission package
	// Import service và repositories của RBAC
	rbacRepo "wnapi/modules/rbac/internal/repository/mysql" // Giả sử dùng MySQL
	rbacService "wnapi/modules/rbac/internal/service"
)

// RBACProviderSet cung cấp các thành phần của module RBAC,
// quan trọng nhất là implementation của sharedPerm.PermissionChecker.
var RBACProviderSet = wire.NewSet(
	// Providers cho các repositories của RBAC
	// Giả sử các New...Repository này nhận *sql.DB (hoặc *gorm.DB) và logger.Logger
	// Chúng ta cần một provider để lấy *sql.DB từ DBManager
	provideRbacDB, // Hàm helper để lấy DB connection cho RBAC
	rbacRepo.NewMySQLUserRoleRepository, // Đổi tên cho phù hợp
	rbacRepo.NewMySQLRolePermissionRepository, // Đổi tên cho phù hợp
	// ... các repo khác

	// Provider cho RBAC PermissionService
	rbacService.NewRBACPermissionService,

	// Bind implementation (*rbacService.PermissionService) với interface (sharedPerm.PermissionChecker)
	// Wire sẽ sử dụng *rbacService.PermissionService khi có yêu cầu sharedPerm.PermissionChecker.
	wire.Bind(new(sharedPerm.PermissionChecker), new(*rbacService.PermissionService)),
)

// provideRbacDB là một helper provider để lấy *sql.DB (hoặc gorm.DB)
// từ DBManager cho các repository của RBAC.
// Điều này giả định DBManager có một method DB() trả về kết nối.
func provideRbacDB(dbm *database.DBManager) *database.WriteReadDB { // Thay *database.WriteReadDB bằng kiểu DB thực tế của bạn
	return dbm.DB() // Giả sử DBManager.DB() trả về kết nối DB
}

// ---- Illustrative Injector (Không sử dụng trực tiếp) ----
// InitializeActualPermissionCheckerExample minh họa cách Wire tạo ra PermissionChecker của RBAC.
func InitializeActualPermissionCheckerExample(log logger.Logger, dbMgr *database.DBManager) (sharedPerm.PermissionChecker, error) {
	wire.Build(RBACProviderSet) // Sử dụng set đã định nghĩa
	return nil, nil
}
```

### 4.2. Khởi tạo Ứng dụng với `wire` (Injector Chính)
File `wire.go` ở thư mục gốc của ứng dụng (ví dụ: `cmd/server/wire.go` hoặc `internal/di/wire.go`) sẽ tập hợp tất cả các `ProviderSet` và định nghĩa injector chính để khởi tạo toàn bộ ứng dụng hoặc các service cốt lõi.

**Ví dụ: `cmd/server/wire.go`**
```go
// cmd/server/wire.go
//go:build wireinject
// +build wireinject

package main

import (
	"github.com/google/wire"
	"wnapi/internal/core"         // App struct, Server struct
	"wnapi/internal/pkg/cache"    // Cache interface và provider
	"wnapi/internal/pkg/config"   // Config interface và provider
	"wnapi/internal/pkg/database" // DBManager interface và provider
	"wnapi/internal/pkg/logger"   // Logger interface và provider
	// Import ProviderSet từ shared permission package
	sharedPermissionDI "wnapi/internal/pkg/permission"
	// Import ProviderSet từ module RBAC
	rbacModuleDI "wnapi/modules/rbac/internal/di"
	// Import ProviderSet từ các module chức năng khác (ví dụ: product)
	// productModuleDI "wnapi/modules/product/internal/di"
	// ...
)

// FullAppSet tổng hợp tất cả các provider set cần thiết cho ứng dụng.
var FullAppSet = wire.NewSet(
	// Core Application Providers
	config.DefaultConfigProviderSet,   // Giả định config package có ProviderSet
	logger.DefaultLoggerProviderSet,   // Giả định logger package có ProviderSet
	cache.DefaultCacheProviderSet,     // Giả định cache package có ProviderSet
	database.DefaultDBProviderSet, // Giả định database package có ProviderSet

	// Shared Permission Package Providers
	sharedPermissionDI.PermissionPkgSet,

	// Module Specific Providers
	rbacModuleDI.RBACProviderSet, // Cung cấp PermissionChecker thực tế (un-cached)
	// productModuleDI.ProductModuleSet, // Khi module product được wire-hóa
	// ... các module khác

	// Application Core Providers (App, Server)
	core.NewServer, // Giả sử NewServer nhận các dependencies như config, logger, và các router/handler
	core.NewApp,    // Giả sử NewApp nhận các dependencies như config, logger, db, cache, MiddlewareFactory
)

// InitializeServerApp là injector chính để tạo instance của *core.Server.
// Nó cũng trả về một cleanup function để giải phóng tài nguyên.
func InitializeServerApp() (*core.Server, func(), error) {
	wire.Build(FullAppSet)
	return nil, nil, nil // Wire sẽ generate implementation
}
```
Sau khi chạy `wire` (ví dụ: `go generate ./...` hoặc `wire cmd/server/wire.go`), file `cmd/server/wire_gen.go` sẽ được tạo.

**Trong `cmd/server/main.go`:**
```go
package main

import (
	"context"
	"log"
	// ...
)

func main() {
	// Khởi tạo server và các dependencies thông qua Wire injector
	serverApp, cleanup, err := InitializeServerApp()
	if err != nil {
		log.Fatalf("Không thể khởi tạo ứng dụng: %v", err)
	}
	defer cleanup() // Đảm bảo cleanup được gọi khi main kết thúc

	// Khởi động server (ví dụ)
	// serverApp phải có method Start() hoặc tương tự
	if err := serverApp.Start(context.Background()); err != nil {
		log.Fatalf("Lỗi khi chạy server: %v", err)
	}
}
```

### 4.3. Cấu trúc `core.App` và cách nhận Dependencies từ `wire`
Struct `core.App` (hoặc một struct tương tự quản lý state của ứng dụng) sẽ nhận các dependencies đã được `wire` giải quyết thông qua constructor của nó.

**Ví dụ: `internal/core/app.go`**
```go
package core

import (
	"wnapi/internal/pkg/cache"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission" // Để lấy MiddlewareFactory
	// ... các import khác
)

// App chứa các thành phần cốt lõi của ứng dụng.
type App struct {
	Config            config.Config
	Logger            logger.Logger
	DBManager         *database.DBManager // Hoặc DB connection trực tiếp
	Cache             cache.Cache
	MiddlewareFactory *permission.MiddlewareFactory // Quan trọng: được inject bởi Wire

	// Có thể chứa các services hoặc managers khác được inject
}

// NewApp là constructor cho App, được google/wire sử dụng.
// Các tham số của nó là dependencies sẽ được Wire tự động inject.
func NewApp(
	cfg config.Config,
	log logger.Logger,
	dbm *database.DBManager,
	appCache cache.Cache,
	mwFactory *permission.MiddlewareFactory, // Wire sẽ tạo và inject cái này
) *App {
	return &App{
		Config:            cfg,
		Logger:            log,
		DBManager:         dbm,
		Cache:             appCache,
		MiddlewareFactory: mwFactory,
	}
}

// Các methods khác của App (ví dụ: GetLogger, GetDB, GetCache, GetMiddlewareFactory)
// có thể không cần thiết nếu các module được thiết kế để nhận dependencies trực tiếp
// qua constructor của chúng (cũng được Wire inject).
func (a *App) GetMiddlewareFactory() *permission.MiddlewareFactory {
    return a.MiddlewareFactory
}
```

## 5. Tích hợp vào Modules Chức năng

Mỗi module chức năng (ví dụ: `products`, `orders`) sẽ sử dụng `MiddlewareFactory` đã được inject để bảo vệ các route của nó.

### 5.1. Thiết lập `wire` cho Module Chức năng (Ví dụ: Module Product)
Tương tự như module RBAC, module Product cũng cần có file `wire.go` riêng để định nghĩa các providers cho service, handler của nó.

**Ví dụ: `modules/product/internal/service/product_service.go`**
(Service của module Product)
```go
package service
// ...
type ProductService struct {
    // repo repository.ProductRepository
    logger logger.Logger
}
func NewProductService(log logger.Logger /*, repo ...*/) *ProductService {
    return &ProductService{logger: log}
}
// ... các method của service
```

**Ví dụ: `modules/product/internal/api/handler.go`**
(Handler của module Product, nhận `MiddlewareFactory`)
```go
package api

import (
	"github.com/gin-gonic/gin"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission" // Để dùng MiddlewareFactory
	productService "wnapi/modules/product/internal/service"
)

type ProductHandler struct {
	service           *productService.ProductService
	middlewareFactory *permission.MiddlewareFactory // Được inject
	logger            logger.Logger
}

// NewProductHandler là constructor, được Wire sử dụng.
func NewProductHandler(
	svc *productService.ProductService,
	mwFactory *permission.MiddlewareFactory, // Dependency được inject
	log logger.Logger,
) *ProductHandler {
	return &ProductHandler{
		service:           svc,
		middlewareFactory: mwFactory,
		logger:            log,
	}
}

// RegisterRoutes đăng ký các route cho module Product.
func (h *ProductHandler) RegisterRoutes(router *gin.RouterGroup) { // Nhận RouterGroup
	// Sử dụng h.middlewareFactory để áp dụng permission middleware
	router.POST("",
		h.middlewareFactory.RequirePermission("products.create"), // Sử dụng permission string
		h.CreateProductHandler,
	)
	router.GET("",
		h.middlewareFactory.RequirePermission(permission.BuildCRUDPermissions(permission.ExampleModule)[4]), // Ví dụ dùng builder
		h.ListProductsHandler,
	)
    // ... các routes khác
}

// Handler methods (ví dụ)
func (h *ProductHandler) CreateProductHandler(c *gin.Context) {
	h.logger.InfoContext(c.Request.Context(), "Handling CreateProduct request")
	// ... logic
	c.JSON(200, gin.H{"message": "Product created (placeholder)"})
}
func (h *ProductHandler) ListProductsHandler(c *gin.Context) {
	h.logger.InfoContext(c.Request.Context(), "Handling ListProducts request")
	// ... logic
	c.JSON(200, gin.H{"message": "Products list (placeholder)"})
}
```

**Ví dụ: `modules/product/internal/di/wire.go`**
```go
// modules/product/internal/di/wire.go
//go:build wireinject
// +build wireinject

package di

import (
	"github.com/google/wire"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission" // Để inject MiddlewareFactory
	productAPI "wnapi/modules/product/internal/api"
	productService "wnapi/modules/product/internal/service"
	// Import các dependencies khác cho ProductService nếu có (ví dụ: repository)
)

// ProductModuleSet cung cấp các thành phần của module Product.
var ProductModuleSet = wire.NewSet(
	productService.NewProductService,
	productAPI.NewProductHandler, // Handler này cần MiddlewareFactory
	// Nếu có repository:
	// productRepo.NewProductRepository,
	// wire.Bind(new(repository.ProductRepository), new(*productRepo.ConcreteProductRepository)),
)

// ---- Illustrative Injector (Không sử dụng trực tiếp) ----
func InitializeProductHandlerExample(
	log logger.Logger,
	mwFactory *permission.MiddlewareFactory,
	// ... các dependencies khác cho ProductService
) (*productAPI.ProductHandler, error) {
	wire.Build(ProductModuleSet)
	return nil, nil
}
```
Sau đó, `ProductModuleSet` này sẽ được thêm vào `FullAppSet` trong `cmd/server/wire.go`.

### 5.2. Sử dụng `MiddlewareFactory` trong Routes của Module
Như đã thấy trong `modules/product/internal/api/handler.go` (Mục 5.1), `ProductHandler` nhận `MiddlewareFactory` qua constructor (do `wire` inject). Sau đó, nó sử dụng `middlewareFactory` để áp dụng các middleware kiểm tra quyền cho từng route.

**Trong `internal/core/server.go` (hoặc nơi bạn đăng ký routes của các module):**
```go
package core

import (
	"context"
	"github.com/gin-gonic/gin"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
    // Import các handler của module mà Wire đã khởi tạo
    // productAPI "wnapi/modules/product/internal/api"
)

type Server struct {
	config config.Config
	logger logger.Logger
	engine *gin.Engine
    // productHandler *productAPI.ProductHandler // Được inject bởi Wire
	// ... các handler khác
}

// NewServer là constructor, được Wire sử dụng.
func NewServer(
    cfg config.Config,
    log logger.Logger,
    // productHdl *productAPI.ProductHandler, // Wire sẽ inject handler này
    // ... các handler khác
) *Server {
	engine := gin.New() // Hoặc gin.Default()
	// Setup middleware chung cho engine (logger, recovery, cors, ...)

	s := &Server{
		config: cfg,
		logger: log,
		engine: engine,
        // productHandler: productHdl,
	}
	s.setupRoutes()
	return s
}

func (s *Server) setupRoutes() {
	apiV1 := s.engine.Group("/api/v1")

    // Đăng ký routes cho module Product
    // if s.productHandler != nil {
    //     productRoutes := apiV1.Group("/products")
    //     s.productHandler.RegisterRoutes(productRoutes) // Gọi method RegisterRoutes của handler
    // }

	// Đăng ký routes cho các module khác
}

func (s *Server) Start(ctx context.Context) error {
    // Lấy port từ config
	// return s.engine.Run(":" + s.config.GetString("server.port"))
    s.logger.Info("Server starting...")
    return s.engine.Run(":8080") // Ví dụ port
}
```
Để `NewServer` nhận được `productHdl` (product handler), `ProductModuleSet` (từ `modules/product/internal/di/wire.go`) phải được bao gồm trong `FullAppSet` (từ `cmd/server/wire.go`).

## 6. Chạy `wire` để Generate Code
Sau khi đã định nghĩa tất cả các `wire.go` files (providers, provider sets, và injectors), bạn cần chạy lệnh `wire` để nó tự động tạo ra các file `*_wire_gen.go`.

Mở terminal ở thư mục gốc của dự án và chạy:
```bash
go generate ./...
```
Hoặc, nếu bạn muốn chạy `wire` trực tiếp cho từng file injector (ít phổ biến hơn):
```bash
wire cmd/server/wire.go
wire internal/pkg/permission/wire.go
# ... và các file wire.go khác chứa injectors
```
Lệnh `go generate ./...` sẽ tìm tất cả các file có `//go:generate wire` hoặc các file `wire.go` với build tag `wireinject` và chạy `wire` cho chúng.

Nếu có lỗi trong quá trình `wire` phân tích dependencies (ví dụ: thiếu provider, cyclical dependency), nó sẽ báo lỗi chi tiết để bạn sửa.

## 7. Benefits và Usage Patterns

### 7.1 Lợi ích của Shared Package và `wire`
-   **Code Reusability & Consistency:**
    -   Logic kiểm tra permission, caching, error handling được tập trung ở `internal/pkg/permission`.
    -   Tất cả modules sử dụng cùng một implementation, đảm bảo tính nhất quán.
-   **Centralized Maintenance:**
    -   Bug fixes và improvements chỉ cần thực hiện ở package chia sẻ.
    -   Dễ dàng cập nhật hoặc thay đổi logic phân quyền chung.
-   **Dependency Injection với `wire`:**
    -   Quản lý dependencies rõ ràng, giảm thiểu việc khởi tạo thủ công.
    -   Tăng khả năng test (dễ dàng mock dependencies).
    -   Code dễ hiểu hơn về luồng khởi tạo và phụ thuộc.
-   **Performance Optimization:**
    -   Cơ chế caching được tích hợp sẵn trong `CachedPermissionChecker`.
-   **Standardization:**
    -   Định dạng error response, logging, và naming patterns thống nhất.

### 7.2 Usage Patterns của Middleware
Sử dụng các method của `MiddlewareFactory` để bảo vệ routes:

#### Pattern 1: Yêu cầu một quyền cụ thể
```go
// User cần có quyền "products.create"
protectedRoutes.POST("/products",
    middlewareFactory.RequirePermission("products.create"),
    productHandler.CreateProduct)
```

#### Pattern 2: Yêu cầu ít nhất một trong nhiều quyền
```go
// User cần có quyền "products.read" HOẶC "reports.product.read"
protectedRoutes.GET("/products/:id/extended-view",
    middlewareFactory.RequireAnyPermission("products.read", "reports.product.read"),
    productHandler.GetProductExtendedView)
```

#### Pattern 3: Yêu cầu tất cả các quyền trong danh sách
```go
// User cần có CẢ quyền "products.update" VÀ "products.publish_special"
protectedRoutes.PUT("/products/:id/publish-special",
    middlewareFactory.RequireAllPermissions("products.update", "products.publish_special"),
    productHandler.PublishSpecialProduct)
```

#### Pattern 4: Sử dụng `PermissionBuilder` hoặc hằng số
```go
import permDefs "wnapi/modules/product/permissions" // Giả sử có file định nghĩa permission

// Sử dụng hằng số
protectedRoutes.DELETE("/products/:id",
    middlewareFactory.RequirePermission(permDefs.ProductDelete),
    productHandler.DeleteProduct)

// Sử dụng PermissionBuilder
readProductPermission := permission.NewPermissionBuilder("products").Action(permission.ActionRead).Build()
protectedRoutes.GET("/products/:id",
    middlewareFactory.RequirePermission(readProductPermission),
    productHandler.GetProduct)
```

## 8. Migration Guide (Từ không `wire` sang có `wire`)

Nếu bạn đang có một hệ thống RBAC không sử dụng `wire` và muốn chuyển sang, các bước chính bao gồm:

1.  **Cấu trúc lại Package `permission`:** Đảm bảo package `internal/pkg/permission/` có cấu trúc như mô tả ở Mục 3 (interfaces, factory, cache, constants, errors). Các hàm `New...` cho `MiddlewareFactory` và `CachedPermissionChecker` sẽ trở thành providers.
2.  **Định nghĩa Providers cho Dependencies Cốt lõi:**
    *   Xác định các dependencies cốt lõi của ứng dụng (config, logger, DB, cache).
    *   Tạo các hàm provider cho chúng (ví dụ: `NewLogger()`, `NewConfig()`, `NewDBManager()`, `NewCache()`) và gom chúng vào các `ProviderSet` tương ứng (ví dụ: `logger.DefaultLoggerProviderSet`).
3.  **Tạo `wire.go` cho Module RBAC:**
    *   Trong module RBAC (nơi implement `permission.PermissionChecker` thực tế), tạo file `wire.go`.
    *   Định nghĩa provider cho implementation `permission.PermissionChecker` (ví dụ: `rbacService.NewRBACPermissionService`).
    *   Bind implementation này với interface `permission.PermissionChecker`.
    *   Tạo `RBACProviderSet` bao gồm các provider cần thiết cho module RBAC.
4.  **Tạo `wire.go` cho Package `permission`:**
    *   Trong `internal/pkg/permission/`, tạo file `wire.go`.
    *   Định nghĩa `PermissionPkgSet` bao gồm các provider `NewMiddlewareFactory`, `NewCachedPermissionChecker`, `providePermissionCacheTTL`. Set này sẽ phụ thuộc vào `permission.PermissionChecker` (từ RBAC) và các core dependencies (logger, cache, config).
5.  **Tạo Injector Chính (`cmd/server/wire.go`):**
    *   Tạo file `wire.go` ở thư mục `cmd/server/` (hoặc nơi khởi tạo ứng dụng).
    *   Định nghĩa `FullAppSet` bằng cách gom tất cả các `ProviderSet` (core, rbac, permission, và các module khác).
    *   Định nghĩa injector chính (ví dụ: `InitializeServerApp()`) sử dụng `FullAppSet` để `wire.Build(...)` ra các đối tượng chính của ứng dụng (ví dụ: `*core.Server`, `*core.App`).
6.  **Cập nhật `main.go`:**
    *   Gọi injector chính (ví dụ: `InitializeServerApp()`) trong hàm `main()` để khởi tạo ứng dụng.
7.  **Wire-hóa các Module Chức năng:**
    *   Với mỗi module chức năng (ví dụ: product):
        *   Thay đổi constructor của service và handler để nhận dependencies (bao gồm `*permission.MiddlewareFactory` cho handler) qua tham số.
        *   Tạo file `wire.go` cho module, định nghĩa các provider và `ModuleSet`.
        *   Thêm `ModuleSet` này vào `FullAppSet` trong injector chính.
8.  **Chạy `go generate ./...`:** Để `wire` tạo code.
9.  **Xóa Code Khởi tạo Thủ công:** Loại bỏ tất cả các lời gọi `New...` thủ công mà giờ đây đã được `wire` quản lý.

## 9. Best Practices

### 9.1 Nguyên tắc Bảo mật
1.  **Kiểm tra quyền ở Backend (Middleware Level):** Luôn luôn kiểm tra quyền ở phía server. Không bao giờ tin tưởng vào việc kiểm tra quyền từ client-side (frontend).
2.  **Principle of Least Privilege (Nguyên tắc Đặc quyền Tối thiểu):** Chỉ cấp cho người dùng và vai trò những quyền tối thiểu cần thiết để họ thực hiện công việc. Tránh cấp các quyền `manage` một cách bừa bãi.
3.  **Tenant Isolation (Cô lập Tenant):** Trong kiến trúc multi-tenant, đảm bảo mọi truy vấn dữ liệu và kiểm tra quyền đều được lọc theo `TenantID` của người dùng hiện tại.
4.  **Validate Input:** Luôn validate các permission string (nếu chúng được truyền động) để tránh các vấn đề tiềm ẩn, mặc dù với `PermissionBuilder` và hằng số, nguy cơ này giảm đi.
5.  **Audit Logging:** Ghi log chi tiết các lần từ chối quyền và các hành động nhạy cảm liên quan đến quản lý quyền.

### 9.2 Performance Optimization
1.  **Caching Permission Checks:** Sử dụng `CachedPermissionChecker` với một hệ thống cache (như Redis, Memcached) để giảm tải cho database. Điều chỉnh TTL của cache cho phù hợp.
2.  **Chiến lược Cache Invalidation Hiệu quả:** Cân nhắc kỹ lưgentcách invalidate cache. Xóa theo pattern có thể tốn kém. Các giải pháp thay thế bao gồm versioning cache key hoặc xóa key cụ thể.
3.  **Tối ưu Truy vấn Database:** Đảm bảo các bảng liên quan đến RBAC (`user_roles`, `role_permissions`, `permissions`) được đánh index hợp lý trên các cột thường xuyên được query (ví dụ: `user_id`, `tenant_id`, `role_id`, `permission_code`).
4.  **Tránh N+1 Query:** Khi lấy danh sách quyền cho một user, cố gắng load tất cả trong một vài query thay vì query cho từng role/permission.

### 9.3 Error Handling

1. **Consistent Error Responses:** Sử dụng package `internal/pkg/response` chuẩn hóa cho tất cả các lỗi liên quan đến phân quyền, đảm bảo tính nhất quán trong toàn bộ hệ thống.
2. **Meaningful Error Codes và Messages:** Cung cấp error code (`ErrorCodePermissionDenied`) và message rõ ràng để client và developer dễ dàng hiểu vấn đề.
3. **Logging Chi tiết:**
   - Log tất cả các trường hợp từ chối quyền (`AbortWithPermissionDenied`) với thông tin `UserID`, `TenantID`, `RequestedPermission`, `Path`.
   - Log các lỗi khi kiểm tra quyền (`AbortWithPermissionCheckError`) với chi tiết lỗi từ hệ thống.

### 9.4 Testing
1.  **Unit Tests:**
    *   Test `PermissionBuilder` để đảm bảo nó tạo chuỗi permission đúng.
    *   Test các hàm helper trong `permission/constants.go`.
    *   Test `MiddlewareFactory` bằng cách mock `PermissionChecker` và `logger.Logger`, sau đó thực thi `gin.HandlerFunc` được tạo ra với `gin.CreateTestContext` để kiểm tra xem `c.Next()` hoặc các hàm `AbortWith...` có được gọi đúng không.
    *   Test `CachedPermissionChecker` bằng cách mock `PermissionChecker` (delegate) và `cache.Cache` để kiểm tra logic cache hit/miss và set/get.
    *   Test implementation của `PermissionChecker` trong module RBAC (ví dụ: `rbacService.PermissionService`) với mock repositories để kiểm tra logic truy vấn quyền.
2.  **Integration Tests:**
    *   Test luồng từ route -> middleware -> service với một test database và test cache instance.
    *   Kiểm tra xem việc gán vai trò, gán quyền cho vai trò có ảnh hưởng đúng đến kết quả kiểm tra quyền không.
3.  **`google/wire` Tests:**
    *   `wire` chủ yếu là công cụ compile-time. Nếu `wire` build thành công, các dependencies đã được kết nối đúng.
    *   Các lỗi thường là do thiếu provider hoặc sai type. `wire` sẽ báo lỗi khi chạy lệnh `wire` hoặc `go generate`.
    *   Test chính của bạn sẽ tập trung vào việc các component được `wire` inject hoạt động đúng như mong đợi.

## 10. Troubleshooting

### 10.1 Các lỗi thường gặp (Từ phía client hoặc logic)
1.  **`"AUTHENTICATION_REQUIRED"` (HTTP 401):**
    *   Nguyên nhân: JWT token không được cung cấp, không hợp lệ, hoặc đã hết hạn.
    *   Kiểm tra: Header `Authorization`, tính hợp lệ của token.
2.  **`"TENANT_REQUIRED"` (HTTP 401/400/403):**
    *   Nguyên nhân: Không tìm thấy `TenantID` trong context, hoặc `TenantID` không hợp lệ.
    *   Kiểm tra: Tenant middleware có hoạt động đúng không, `TenantID` có được đặt vào context không.
3.  **`"PERMISSION_DENIED"` (HTTP 403):**
    *   Nguyên nhân: Người dùng đã xác thực và có tenant hợp lệ, nhưng không có quyền cần thiết để thực hiện hành động.
    *   Kiểm tra: Dữ liệu trong các bảng `user_roles`, `role_permissions` có đúng không. Vai trò của người dùng có được gán quyền đó không.
4.  **`"PERMISSION_CHECK_ERROR"` (HTTP 500):**
    *   Nguyên nhân: Lỗi hệ thống khi cố gắng kiểm tra quyền (ví dụ: lỗi kết nối DB, lỗi logic trong `PermissionChecker` của RBAC).
    *   Kiểm tra: Logs của server để xem chi tiết lỗi.

### 10.2 Debug Tips
1.  **Kiểm tra Thứ tự Middleware:** Đảm bảo thứ tự **Tenant → Auth → RBAC Middleware** được áp dụng đúng cho group route.
2.  **Logging Chi tiết:**
    *   Trong `MiddlewareFactory`, thêm log để xem `UserID`, `TenantID`, `permission` được yêu cầu.
    *   Trong `CachedPermissionChecker`, log cache hit/miss, key được tạo.
    *   Trong implementation `PermissionChecker` của RBAC, log các bước kiểm tra quyền.
3.  **Verify Database Data:** Kiểm tra thủ công dữ liệu trong các bảng `users`, `tenants`, `roles`, `permissions`, `user_roles`, `role_permissions` để đảm bảo chúng được thiết lập chính xác.
4.  **Sử dụng Postman/curl:** Tạo request với JWT token hợp lệ (nếu cần) và kiểm tra response, headers.
5.  **Debug với Breakpoints:** Nếu có thể, sử dụng debugger để đi qua luồng kiểm tra quyền.

### 10.3 Lỗi liên quan đến `wire`
1.  **"Provider for X not found" / "Missing binding for Y":**
    *   Nguyên nhân: `wire` không tìm thấy hàm provider nào tạo ra kiểu `X`, hoặc không biết interface `Y` được implement bởi concrete type nào.
    *   Khắc phục:
        *   Đảm bảo có hàm `New...()` cho `X` và hàm đó được thêm vào một `ProviderSet` đang được sử dụng.
        *   Sử dụng `wire.Bind(new(InterfaceType), new(*ConcreteType))` nếu cần.
        *   Kiểm tra xem tất cả `ProviderSet` cần thiết đã được gộp vào `wire.Build(...)` trong injector chưa.
2.  **"Cyclical Dependency":**
    *   Nguyên nhân: Ví dụ, Service A cần Service B, và Service B lại cần Service A.
    *   Khắc phục: Cấu trúc lại code để phá vỡ vòng lặp. Có thể tách một interface ra, hoặc sử dụng setter injection (ít phổ biến với `wire`).
3.  **Type Mismatch:**
    *   Nguyên nhân: Provider trả về kiểu `*TypeA` nhưng dependency lại yêu cầu `TypeA` (hoặc ngược lại).
    *   Khắc phục: Điều chỉnh kiểu trả về của provider hoặc kiểu tham số của constructor.
4.  **Lỗi trong file `*_wire_gen.go`:**
    *   **Không bao giờ sửa file `*_wire_gen.go` thủ công.**
    *   Lỗi ở đây là kết quả của việc `wire` không thể tạo code đúng. Hãy tìm lỗi trong các file `wire.go` của bạn.
    *   Xóa file `*_wire_gen.go` và chạy lại `go generate ./...` sau khi sửa lỗi.

---

Tài liệu này cung cấp một hướng dẫn toàn diện về cách thiết kế và tích hợp hệ thống phân quyền RBAC sử dụng `google/wire`. Việc tuân thủ các quy tắc và thực hành tốt nhất sẽ giúp xây dựng một hệ thống mạnh mẽ, dễ bảo trì và mở rộng.