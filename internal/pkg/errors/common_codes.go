package errors

// <PERSON><PERSON><PERSON> mã lỗi chung (Common Error Codes)
const (
	// Lỗi chung
	ErrCodeUnknown          ErrorCode = "UNKNOWN_ERROR"
	ErrCodeValidationFailed ErrorCode = "VALIDATION_FAILED"
	ErrCodeBadRequest       ErrorCode = "BAD_REQUEST"
	ErrCodeInternalServer   ErrorCode = "INTERNAL_SERVER_ERROR"
	ErrCodeNotFound         ErrorCode = "NOT_FOUND"
	ErrCodeForbidden        ErrorCode = "FORBIDDEN"
	ErrCodeConflict         ErrorCode = "CONFLICT"
	ErrCodeUnauthorized     ErrorCode = "UNAUTHORIZED"
	ErrCodeTimeout          ErrorCode = "TIMEOUT"
	ErrCodeTooManyRequests  ErrorCode = "TOO_MANY_REQUESTS"

	// Lỗi database
	ErrCodeDBConnectionFailed    ErrorCode = "DB_CONNECTION_FAILED"
	ErrCodeDBQueryFailed         ErrorCode = "DB_QUERY_FAILED"
	ErrCodeDBTransactionFailed   ErrorCode = "DB_TRANSACTION_FAILED"
	ErrCodeDBConstraintViolation ErrorCode = "DB_CONSTRAINT_VIOLATION"
	ErrCodeDBDeadlock            ErrorCode = "DB_DEADLOCK"
	ErrCodeDBTimeout             ErrorCode = "DB_TIMEOUT"

	// Lỗi file/upload
	ErrCodeFileTooLarge     ErrorCode = "FILE_TOO_LARGE"
	ErrCodeInvalidFileType  ErrorCode = "INVALID_FILE_TYPE"
	ErrCodeFileUploadFailed ErrorCode = "FILE_UPLOAD_FAILED"
	ErrCodeFileNotFound     ErrorCode = "FILE_NOT_FOUND"

	// Lỗi mạng/dịch vụ ngoài
	ErrCodeExternalServiceUnavailable ErrorCode = "EXTERNAL_SERVICE_UNAVAILABLE"
	ErrCodeNetworkTimeout             ErrorCode = "NETWORK_TIMEOUT"
	ErrCodeInvalidResponse            ErrorCode = "INVALID_RESPONSE"
)

// CommonErrorMessages chứa thông báo lỗi đa ngôn ngữ cho các mã lỗi chung
var CommonErrorMessages = map[ErrorCode]map[string]string{
	// Thông báo lỗi chung
	ErrCodeUnknown: {
		"en": "An unknown error occurred",
		"vi": "Đã xảy ra lỗi không xác định",
	},
	ErrCodeValidationFailed: {
		"en": "Validation failed",
		"vi": "Dữ liệu không hợp lệ",
	},
	ErrCodeBadRequest: {
		"en": "Bad request",
		"vi": "Yêu cầu không hợp lệ",
	},
	ErrCodeInternalServer: {
		"en": "Internal server error",
		"vi": "Lỗi máy chủ nội bộ",
	},
	ErrCodeNotFound: {
		"en": "Resource not found",
		"vi": "Không tìm thấy tài nguyên",
	},
	ErrCodeForbidden: {
		"en": "Access forbidden",
		"vi": "Truy cập bị từ chối",
	},
	ErrCodeConflict: {
		"en": "Resource conflict",
		"vi": "Xung đột tài nguyên",
	},
	ErrCodeUnauthorized: {
		"en": "Unauthorized access",
		"vi": "Truy cập không được phép",
	},
	ErrCodeTimeout: {
		"en": "Request timeout",
		"vi": "Yêu cầu đã hết thời gian",
	},
	ErrCodeTooManyRequests: {
		"en": "Too many requests",
		"vi": "Quá nhiều yêu cầu",
	},

	// Thông báo lỗi database
	ErrCodeDBConnectionFailed: {
		"en": "Database connection failed",
		"vi": "Kết nối cơ sở dữ liệu thất bại",
	},
	ErrCodeDBQueryFailed: {
		"en": "Database query failed",
		"vi": "Truy vấn cơ sở dữ liệu thất bại",
	},
}

// GetCommonMessage trả về thông báo lỗi chung theo ngôn ngữ
func GetCommonMessage(code ErrorCode, lang string) string {
	if messages, exists := CommonErrorMessages[code]; exists {
		if message, exists := messages[lang]; exists {
			return message
		}
		// Fallback to English
		if message, exists := messages["en"]; exists {
			return message
		}
	}
	return string(code)
}
